(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/DocumentEditor.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>DocumentEditor
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$react$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/react/dist/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$starter$2d$kit$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tiptap/starter-kit/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiptap$2d$pagination$2d$plus$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/tiptap-pagination-plus/dist/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiptap$2d$pagination$2d$plus$2f$dist$2f$PaginationPlus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiptap-pagination-plus/dist/PaginationPlus.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiptap$2d$pagination$2d$plus$2f$dist$2f$TablePlus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiptap-pagination-plus/dist/TablePlus.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiptap$2d$pagination$2d$plus$2f$dist$2f$TableRowPlus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiptap-pagination-plus/dist/TableRowPlus.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiptap$2d$pagination$2d$plus$2f$dist$2f$TableCellPlus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiptap-pagination-plus/dist/TableCellPlus.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiptap$2d$pagination$2d$plus$2f$dist$2f$TableHeaderPlus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiptap-pagination-plus/dist/TableHeaderPlus.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
function DocumentEditor(param) {
    let { content = '', onChange } = param;
    _s();
    const [isMounted, setIsMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const editor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$react$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEditor"])({
        extensions: [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$starter$2d$kit$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiptap$2d$pagination$2d$plus$2f$dist$2f$TablePlus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TablePlus"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiptap$2d$pagination$2d$plus$2f$dist$2f$TableRowPlus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRowPlus"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiptap$2d$pagination$2d$plus$2f$dist$2f$TableCellPlus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCellPlus"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiptap$2d$pagination$2d$plus$2f$dist$2f$TableHeaderPlus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHeaderPlus"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiptap$2d$pagination$2d$plus$2f$dist$2f$PaginationPlus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationPlus"].configure({
                pageHeight: 842,
                pageGap: 20,
                pageBreakBackground: "#f7f7f7",
                pageHeaderHeight: 50,
                footerText: "Document Orbis" // Custom footer text
            })
        ],
        content,
        immediatelyRender: false,
        onUpdate: {
            "DocumentEditor.useEditor[editor]": (param)=>{
                let { editor } = param;
                const html = editor.getHTML();
                onChange === null || onChange === void 0 ? void 0 : onChange(html);
            }
        }["DocumentEditor.useEditor[editor]"],
        editorProps: {
            attributes: {
                class: 'tiptap-editor focus:outline-none min-h-screen p-4'
            }
        }
    });
    // Gérer l'hydratation côté client
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DocumentEditor.useEffect": ()=>{
            setIsMounted(true);
        }
    }["DocumentEditor.useEffect"], []);
    // Éviter les erreurs d'hydratation
    if (!isMounted) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-100 p-8",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-4xl mx-auto bg-white shadow-lg",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "h-96 flex items-center justify-center text-gray-500",
                    children: "Chargement de l'éditeur..."
                }, void 0, false, {
                    fileName: "[project]/src/components/DocumentEditor.tsx",
                    lineNumber: 61,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/DocumentEditor.tsx",
                lineNumber: 60,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/DocumentEditor.tsx",
            lineNumber: 59,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-100",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$react$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["EditorContent"], {
            editor: editor
        }, void 0, false, {
            fileName: "[project]/src/components/DocumentEditor.tsx",
            lineNumber: 71,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/DocumentEditor.tsx",
        lineNumber: 70,
        columnNumber: 5
    }, this);
}
_s(DocumentEditor, "9xHOyd0xAzBU+AeSopoO7yYVK/c=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tiptap$2f$react$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEditor"]
    ];
});
_c = DocumentEditor;
var _c;
__turbopack_context__.k.register(_c, "DocumentEditor");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_components_DocumentEditor_tsx_9c454a9d._.js.map