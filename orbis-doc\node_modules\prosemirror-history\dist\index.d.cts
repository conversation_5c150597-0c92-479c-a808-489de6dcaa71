import { Transaction, Plugin, Command, EditorState } from 'prosemirror-state';

/**
Set a flag on the given transaction that will prevent further steps
from being appended to an existing history event (so that they
require a separate undo command to undo).
*/
declare function closeHistory(tr: Transaction): Transaction;
interface HistoryOptions {
    /**
    The amount of history events that are collected before the
    oldest events are discarded. Defaults to 100.
    */
    depth?: number;
    /**
    The delay between changes after which a new group should be
    started. Defaults to 500 (milliseconds). Note that when changes
    aren't adjacent, a new group is always started.
    */
    newGroupDelay?: number;
}
/**
Returns a plugin that enables the undo history for an editor. The
plugin will track undo and redo stacks, which can be used with the
[`undo`](https://prosemirror.net/docs/ref/#history.undo) and [`redo`](https://prosemirror.net/docs/ref/#history.redo) commands.

You can set an `"addToHistory"` [metadata
property](https://prosemirror.net/docs/ref/#state.Transaction.setMeta) of `false` on a transaction
to prevent it from being rolled back by undo.
*/
declare function history(config?: HistoryOptions): Plugin;
/**
A command function that undoes the last change, if any.
*/
declare const undo: Command;
/**
A command function that redoes the last undone change, if any.
*/
declare const redo: Command;
/**
A command function that undoes the last change. Don't scroll the
selection into view.
*/
declare const undoNoScroll: Command;
/**
A command function that redoes the last undone change. Don't
scroll the selection into view.
*/
declare const redoNoScroll: Command;
/**
The amount of undoable events available in a given state.
*/
declare function undoDepth(state: EditorState): any;
/**
The amount of redoable events available in a given editor state.
*/
declare function redoDepth(state: EditorState): any;

export { closeHistory, history, redo, redoDepth, redoNoScroll, undo, undoDepth, undoNoScroll };
