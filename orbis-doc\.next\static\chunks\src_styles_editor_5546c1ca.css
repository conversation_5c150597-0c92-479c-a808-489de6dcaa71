/* [project]/src/styles/editor.css [app-client] (css) */
.tiptap-editor {
  background: #f7f7f7;
  border: none;
  outline: none;
  min-height: 100vh;
}

.tiptap-editor .page {
  box-sizing: border-box;
  background: #fff;
  width: 595px;
  min-height: 842px;
  margin: 0 auto 20px;
  padding: 60px 80px;
  position: relative;
  box-shadow: 0 4px 6px rgba(0, 0, 0, .1);
}

.tiptap-pagination-header {
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
  align-items: center;
  height: 15mm;
  font-size: 12px;
  display: flex;
  position: absolute;
  top: 10mm;
  left: 25mm;
  right: 25mm;
}

.tiptap-pagination-footer {
  color: #6b7280;
  border-top: 1px solid #e5e7eb;
  justify-content: center;
  align-items: center;
  height: 15mm;
  font-size: 12px;
  display: flex;
  position: absolute;
  bottom: 10mm;
  left: 25mm;
  right: 25mm;
}

.tiptap-pagination-content {
  min-height: 247mm;
  padding-top: 20mm;
  padding-bottom: 20mm;
}

.tiptap-editor p {
  margin: 0 0 1em;
  line-height: 1.6;
}

.tiptap-editor h1, .tiptap-editor h2, .tiptap-editor h3, .tiptap-editor h4, .tiptap-editor h5, .tiptap-editor h6 {
  margin: 1.5em 0 .5em;
  font-weight: bold;
}

.tiptap-editor h1 {
  font-size: 2em;
}

.tiptap-editor h2 {
  font-size: 1.5em;
}

.tiptap-editor h3 {
  font-size: 1.25em;
}

.tiptap-editor ul, .tiptap-editor ol {
  margin: 1em 0;
  padding-left: 2em;
}

.tiptap-editor blockquote {
  border-left: 4px solid #e5e7eb;
  margin: 1em 0;
  padding-left: 1em;
  font-style: italic;
}

.tiptap-editor code {
  background: #f3f4f6;
  border-radius: 3px;
  padding: .2em .4em;
  font-family: monospace;
}

.tiptap-editor pre {
  background: #f3f4f6;
  border-radius: 6px;
  margin: 1em 0;
  padding: 1em;
  overflow-x: auto;
}

.tiptap-editor pre code {
  background: none;
  padding: 0;
}

.tiptap-editor .pagination-plus-page {
  box-sizing: border-box !important;
  background: #fff !important;
  width: 595px !important;
  min-height: 842px !important;
  margin: 0 auto 20px !important;
  padding: 60px 80px !important;
  position: relative !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, .1) !important;
}

.tiptap-editor .pagination-plus-header {
  color: #6b7280 !important;
  border-bottom: 1px solid #e5e7eb !important;
  align-items: center !important;
  height: 30px !important;
  font-size: 12px !important;
  display: flex !important;
  position: absolute !important;
  top: 20px !important;
  left: 80px !important;
  right: 80px !important;
}

.tiptap-editor .pagination-plus-footer {
  color: #6b7280 !important;
  border-top: 1px solid #e5e7eb !important;
  justify-content: center !important;
  align-items: center !important;
  height: 30px !important;
  font-size: 12px !important;
  display: flex !important;
  position: absolute !important;
  bottom: 20px !important;
  left: 80px !important;
  right: 80px !important;
}

@media screen and (max-width: 768px) {
  .tiptap-editor .page, .tiptap-editor .pagination-plus-page {
    width: 100% !important;
    min-height: auto !important;
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
    margin: 0 0 20px !important;
    padding: 20px !important;
  }

  .tiptap-pagination-header, .tiptap-pagination-footer, .tiptap-editor .pagination-plus-header, .tiptap-editor .pagination-plus-footer {
    margin: 10px 0 !important;
    position: static !important;
  }

  .tiptap-pagination-content {
    min-height: auto !important;
    padding: 0 !important;
  }
}

/*# sourceMappingURL=src_styles_editor_5546c1ca.css.map*/