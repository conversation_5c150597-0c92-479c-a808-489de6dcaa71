/* [project]/src/styles/editor.css [app-client] (css) */
.tiptap-editor {
  border: none;
  outline: none;
}

.tiptap-pagination-page {
  box-sizing: border-box;
  background: #fff;
  width: 210mm;
  min-height: 297mm;
  margin: 0 auto 20px;
  padding: 25mm;
  position: relative;
  box-shadow: 0 4px 6px rgba(0, 0, 0, .1);
}

.tiptap-pagination-header {
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
  align-items: center;
  height: 15mm;
  font-size: 12px;
  display: flex;
  position: absolute;
  top: 10mm;
  left: 25mm;
  right: 25mm;
}

.tiptap-pagination-footer {
  color: #6b7280;
  border-top: 1px solid #e5e7eb;
  justify-content: center;
  align-items: center;
  height: 15mm;
  font-size: 12px;
  display: flex;
  position: absolute;
  bottom: 10mm;
  left: 25mm;
  right: 25mm;
}

.tiptap-pagination-content {
  min-height: 247mm;
  padding-top: 20mm;
  padding-bottom: 20mm;
}

.tiptap-editor p {
  margin: 0 0 1em;
  line-height: 1.6;
}

.tiptap-editor h1, .tiptap-editor h2, .tiptap-editor h3, .tiptap-editor h4, .tiptap-editor h5, .tiptap-editor h6 {
  margin: 1.5em 0 .5em;
  font-weight: bold;
}

.tiptap-editor h1 {
  font-size: 2em;
}

.tiptap-editor h2 {
  font-size: 1.5em;
}

.tiptap-editor h3 {
  font-size: 1.25em;
}

.tiptap-editor ul, .tiptap-editor ol {
  margin: 1em 0;
  padding-left: 2em;
}

.tiptap-editor blockquote {
  border-left: 4px solid #e5e7eb;
  margin: 1em 0;
  padding-left: 1em;
  font-style: italic;
}

.tiptap-editor code {
  background: #f3f4f6;
  border-radius: 3px;
  padding: .2em .4em;
  font-family: monospace;
}

.tiptap-editor pre {
  background: #f3f4f6;
  border-radius: 6px;
  margin: 1em 0;
  padding: 1em;
  overflow-x: auto;
}

.tiptap-editor pre code {
  background: none;
  padding: 0;
}

@media screen and (max-width: 768px) {
  .tiptap-pagination-page {
    width: 100%;
    min-height: auto;
    box-shadow: none;
    border: 1px solid #e5e7eb;
    margin: 0 0 20px;
    padding: 20px;
  }

  .tiptap-pagination-header, .tiptap-pagination-footer {
    margin: 10px 0;
    position: static;
  }

  .tiptap-pagination-content {
    min-height: auto;
    padding: 0;
  }
}

/*# sourceMappingURL=src_styles_editor_5546c1ca.css.map*/