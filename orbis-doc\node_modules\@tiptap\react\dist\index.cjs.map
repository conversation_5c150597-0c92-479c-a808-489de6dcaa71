{"version": 3, "sources": ["../src/index.ts", "../src/Context.tsx", "../src/EditorContent.tsx", "../src/useEditor.ts", "../src/useEditorState.ts", "../src/useReactNodeView.ts", "../src/NodeViewContent.tsx", "../src/NodeViewWrapper.tsx", "../src/ReactMarkViewRenderer.tsx", "../src/ReactRenderer.tsx", "../src/ReactNodeViewRenderer.tsx"], "sourcesContent": ["export * from './Context.js'\nexport * from './EditorContent.js'\nexport * from './NodeViewContent.js'\nexport * from './NodeViewWrapper.js'\nexport * from './ReactMarkViewRenderer.js'\nexport * from './ReactNodeViewRenderer.js'\nexport * from './ReactRenderer.js'\nexport * from './types.js'\nexport * from './useEditor.js'\nexport * from './useEditorState.js'\nexport * from './useReactNodeView.js'\nexport * from '@tiptap/core'\n", "import type { Editor } from '@tiptap/core'\nimport type { HTMLAttributes, ReactNode } from 'react'\nimport React, { createContext, useContext, useMemo } from 'react'\n\nimport { EditorContent } from './EditorContent.js'\nimport type { UseEditorOptions } from './useEditor.js'\nimport { useEditor } from './useEditor.js'\n\nexport type EditorContextValue = {\n  editor: Editor | null\n}\n\nexport const EditorContext = createContext<EditorContextValue>({\n  editor: null,\n})\n\nexport const EditorConsumer = EditorContext.Consumer\n\n/**\n * A hook to get the current editor instance.\n */\nexport const useCurrentEditor = () => useContext(EditorContext)\n\nexport type EditorProviderProps = {\n  children?: ReactNode\n  slotBefore?: ReactNode\n  slotAfter?: ReactNode\n  editorContainerProps?: HTMLAttributes<HTMLDivElement>\n} & UseEditorOptions\n\n/**\n * This is the provider component for the editor.\n * It allows the editor to be accessible across the entire component tree\n * with `useCurrentEditor`.\n */\nexport function EditorProvider({\n  children,\n  slotAfter,\n  slotBefore,\n  editorContainerProps = {},\n  ...editorOptions\n}: EditorProviderProps) {\n  const editor = useEditor(editorOptions)\n  const contextValue = useMemo(() => ({ editor }), [editor])\n\n  if (!editor) {\n    return null\n  }\n\n  return (\n    <EditorContext.Provider value={contextValue}>\n      {slotBefore}\n      <EditorConsumer>\n        {({ editor: currentEditor }) => <EditorContent editor={currentEditor} {...editorContainerProps} />}\n      </EditorConsumer>\n      {children}\n      {slotAfter}\n    </EditorContext.Provider>\n  )\n}\n", "import type { Editor } from '@tiptap/core'\nimport type { ForwardedRef, HTMLProps, LegacyRef, MutableRefObject } from 'react'\nimport React, { forwardRef } from 'react'\nimport ReactDOM from 'react-dom'\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js'\n\nimport type { ContentComponent, EditorWithContentComponent } from './Editor.js'\nimport type { ReactRenderer } from './ReactRenderer.js'\n\nconst mergeRefs = <T extends HTMLDivElement>(...refs: Array<MutableRefObject<T> | LegacyRef<T> | undefined>) => {\n  return (node: T) => {\n    refs.forEach(ref => {\n      if (typeof ref === 'function') {\n        ref(node)\n      } else if (ref) {\n        ;(ref as MutableRefObject<T | null>).current = node\n      }\n    })\n  }\n}\n\n/**\n * This component renders all of the editor's node views.\n */\nconst Portals: React.FC<{ contentComponent: ContentComponent }> = ({ contentComponent }) => {\n  // For performance reasons, we render the node view portals on state changes only\n  const renderers = useSyncExternalStore(\n    contentComponent.subscribe,\n    contentComponent.getSnapshot,\n    contentComponent.getServerSnapshot,\n  )\n\n  // This allows us to directly render the portals without any additional wrapper\n  return <>{Object.values(renderers)}</>\n}\n\nexport interface EditorContentProps extends HTMLProps<HTMLDivElement> {\n  editor: Editor | null\n  innerRef?: ForwardedRef<HTMLDivElement | null>\n}\n\nfunction getInstance(): ContentComponent {\n  const subscribers = new Set<() => void>()\n  let renderers: Record<string, React.ReactPortal> = {}\n\n  return {\n    /**\n     * Subscribe to the editor instance's changes.\n     */\n    subscribe(callback: () => void) {\n      subscribers.add(callback)\n      return () => {\n        subscribers.delete(callback)\n      }\n    },\n    getSnapshot() {\n      return renderers\n    },\n    getServerSnapshot() {\n      return renderers\n    },\n    /**\n     * Adds a new NodeView Renderer to the editor.\n     */\n    setRenderer(id: string, renderer: ReactRenderer) {\n      renderers = {\n        ...renderers,\n        [id]: ReactDOM.createPortal(renderer.reactElement, renderer.element, id),\n      }\n\n      subscribers.forEach(subscriber => subscriber())\n    },\n    /**\n     * Removes a NodeView Renderer from the editor.\n     */\n    removeRenderer(id: string) {\n      const nextRenderers = { ...renderers }\n\n      delete nextRenderers[id]\n      renderers = nextRenderers\n      subscribers.forEach(subscriber => subscriber())\n    },\n  }\n}\n\nexport class PureEditorContent extends React.Component<\n  EditorContentProps,\n  { hasContentComponentInitialized: boolean }\n> {\n  editorContentRef: React.RefObject<any>\n\n  initialized: boolean\n\n  unsubscribeToContentComponent?: () => void\n\n  constructor(props: EditorContentProps) {\n    super(props)\n    this.editorContentRef = React.createRef()\n    this.initialized = false\n\n    this.state = {\n      hasContentComponentInitialized: Boolean((props.editor as EditorWithContentComponent | null)?.contentComponent),\n    }\n  }\n\n  componentDidMount() {\n    this.init()\n  }\n\n  componentDidUpdate() {\n    this.init()\n  }\n\n  init() {\n    const editor = this.props.editor as EditorWithContentComponent | null\n\n    if (editor && !editor.isDestroyed && editor.options.element) {\n      if (editor.contentComponent) {\n        return\n      }\n\n      const element = this.editorContentRef.current\n\n      element.append(...editor.options.element.childNodes)\n\n      editor.setOptions({\n        element,\n      })\n\n      editor.contentComponent = getInstance()\n\n      // Has the content component been initialized?\n      if (!this.state.hasContentComponentInitialized) {\n        // Subscribe to the content component\n        this.unsubscribeToContentComponent = editor.contentComponent.subscribe(() => {\n          this.setState(prevState => {\n            if (!prevState.hasContentComponentInitialized) {\n              return {\n                hasContentComponentInitialized: true,\n              }\n            }\n            return prevState\n          })\n\n          // Unsubscribe to previous content component\n          if (this.unsubscribeToContentComponent) {\n            this.unsubscribeToContentComponent()\n          }\n        })\n      }\n\n      editor.createNodeViews()\n\n      this.initialized = true\n    }\n  }\n\n  componentWillUnmount() {\n    const editor = this.props.editor as EditorWithContentComponent | null\n\n    if (!editor) {\n      return\n    }\n\n    this.initialized = false\n\n    if (!editor.isDestroyed) {\n      editor.view.setProps({\n        nodeViews: {},\n      })\n    }\n\n    if (this.unsubscribeToContentComponent) {\n      this.unsubscribeToContentComponent()\n    }\n\n    editor.contentComponent = null\n\n    if (!editor.options.element?.firstChild) {\n      return\n    }\n\n    // TODO using the new editor.mount method might allow us to remove this\n    const newElement = document.createElement('div')\n\n    newElement.append(...editor.options.element.childNodes)\n\n    editor.setOptions({\n      element: newElement,\n    })\n  }\n\n  render() {\n    const { editor, innerRef, ...rest } = this.props\n\n    return (\n      <>\n        <div ref={mergeRefs(innerRef, this.editorContentRef)} {...rest} />\n        {/* @ts-ignore */}\n        {editor?.contentComponent && <Portals contentComponent={editor.contentComponent} />}\n      </>\n    )\n  }\n}\n\n// EditorContent should be re-created whenever the Editor instance changes\nconst EditorContentWithKey = forwardRef<HTMLDivElement, EditorContentProps>(\n  (props: Omit<EditorContentProps, 'innerRef'>, ref) => {\n    const key = React.useMemo(() => {\n      return Math.floor(Math.random() * 0xffffffff).toString()\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.editor])\n\n    // Can't use JSX here because it conflicts with the type definition of Vue's JSX, so use createElement\n    return React.createElement(PureEditorContent, {\n      key,\n      innerRef: ref,\n      ...props,\n    })\n  },\n)\n\nexport const EditorContent = React.memo(EditorContentWithKey)\n", "import { type EditorOptions, Editor } from '@tiptap/core'\nimport type { DependencyList, MutableRefObject } from 'react'\nimport { useDebugValue, useEffect, useRef, useState } from 'react'\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js'\n\nimport { useEditorState } from './useEditorState.js'\n\n// @ts-ignore\nconst isDev = process.env.NODE_ENV !== 'production'\nconst isSSR = typeof window === 'undefined'\nconst isNext = isSSR || Boolean(typeof window !== 'undefined' && (window as any).next)\n\n/**\n * The options for the `useEditor` hook.\n */\nexport type UseEditorOptions = Partial<EditorOptions> & {\n  /**\n   * Whether to render the editor on the first render.\n   * If client-side rendering, set this to `true`.\n   * If server-side rendering, set this to `false`.\n   * @default true\n   */\n  immediatelyRender?: boolean\n  /**\n   * Whether to re-render the editor on each transaction.\n   * This is legacy behavior that will be removed in future versions.\n   * @default false\n   */\n  shouldRerenderOnTransaction?: boolean\n}\n\n/**\n * This class handles the creation, destruction, and re-creation of the editor instance.\n */\nclass EditorInstanceManager {\n  /**\n   * The current editor instance.\n   */\n  private editor: Editor | null = null\n\n  /**\n   * The most recent options to apply to the editor.\n   */\n  private options: MutableRefObject<UseEditorOptions>\n\n  /**\n   * The subscriptions to notify when the editor instance\n   * has been created or destroyed.\n   */\n  private subscriptions = new Set<() => void>()\n\n  /**\n   * A timeout to destroy the editor if it was not mounted within a time frame.\n   */\n  private scheduledDestructionTimeout: ReturnType<typeof setTimeout> | undefined\n\n  /**\n   * Whether the editor has been mounted.\n   */\n  private isComponentMounted = false\n\n  /**\n   * The most recent dependencies array.\n   */\n  private previousDeps: DependencyList | null = null\n\n  /**\n   * The unique instance ID. This is used to identify the editor instance. And will be re-generated for each new instance.\n   */\n  public instanceId = ''\n\n  constructor(options: MutableRefObject<UseEditorOptions>) {\n    this.options = options\n    this.subscriptions = new Set<() => void>()\n    this.setEditor(this.getInitialEditor())\n    this.scheduleDestroy()\n\n    this.getEditor = this.getEditor.bind(this)\n    this.getServerSnapshot = this.getServerSnapshot.bind(this)\n    this.subscribe = this.subscribe.bind(this)\n    this.refreshEditorInstance = this.refreshEditorInstance.bind(this)\n    this.scheduleDestroy = this.scheduleDestroy.bind(this)\n    this.onRender = this.onRender.bind(this)\n    this.createEditor = this.createEditor.bind(this)\n  }\n\n  private setEditor(editor: Editor | null) {\n    this.editor = editor\n    this.instanceId = Math.random().toString(36).slice(2, 9)\n\n    // Notify all subscribers that the editor instance has been created\n    this.subscriptions.forEach(cb => cb())\n  }\n\n  private getInitialEditor() {\n    if (this.options.current.immediatelyRender === undefined) {\n      if (isSSR || isNext) {\n        if (isDev) {\n          /**\n           * Throw an error in development, to make sure the developer is aware that tiptap cannot be SSR'd\n           * and that they need to set `immediatelyRender` to `false` to avoid hydration mismatches.\n           */\n          throw new Error(\n            'Tiptap Error: SSR has been detected, please set `immediatelyRender` explicitly to `false` to avoid hydration mismatches.',\n          )\n        }\n\n        // Best faith effort in production, run the code in the legacy mode to avoid hydration mismatches and errors in production\n        return null\n      }\n\n      // Default to immediately rendering when client-side rendering\n      return this.createEditor()\n    }\n\n    if (this.options.current.immediatelyRender && isSSR && isDev) {\n      // Warn in development, to make sure the developer is aware that tiptap cannot be SSR'd, set `immediatelyRender` to `false` to avoid hydration mismatches.\n      throw new Error(\n        'Tiptap Error: SSR has been detected, and `immediatelyRender` has been set to `true` this is an unsupported configuration that may result in errors, explicitly set `immediatelyRender` to `false` to avoid hydration mismatches.',\n      )\n    }\n\n    if (this.options.current.immediatelyRender) {\n      return this.createEditor()\n    }\n\n    return null\n  }\n\n  /**\n   * Create a new editor instance. And attach event listeners.\n   */\n  private createEditor(): Editor {\n    const optionsToApply: Partial<EditorOptions> = {\n      ...this.options.current,\n      // Always call the most recent version of the callback function by default\n      onBeforeCreate: (...args) => this.options.current.onBeforeCreate?.(...args),\n      onBlur: (...args) => this.options.current.onBlur?.(...args),\n      onCreate: (...args) => this.options.current.onCreate?.(...args),\n      onDestroy: (...args) => this.options.current.onDestroy?.(...args),\n      onFocus: (...args) => this.options.current.onFocus?.(...args),\n      onSelectionUpdate: (...args) => this.options.current.onSelectionUpdate?.(...args),\n      onTransaction: (...args) => this.options.current.onTransaction?.(...args),\n      onUpdate: (...args) => this.options.current.onUpdate?.(...args),\n      onContentError: (...args) => this.options.current.onContentError?.(...args),\n      onDrop: (...args) => this.options.current.onDrop?.(...args),\n      onPaste: (...args) => this.options.current.onPaste?.(...args),\n      onDelete: (...args) => this.options.current.onDelete?.(...args),\n    }\n    const editor = new Editor(optionsToApply)\n\n    // no need to keep track of the event listeners, they will be removed when the editor is destroyed\n\n    return editor\n  }\n\n  /**\n   * Get the current editor instance.\n   */\n  getEditor(): Editor | null {\n    return this.editor\n  }\n\n  /**\n   * Always disable the editor on the server-side.\n   */\n  getServerSnapshot(): null {\n    return null\n  }\n\n  /**\n   * Subscribe to the editor instance's changes.\n   */\n  subscribe(onStoreChange: () => void) {\n    this.subscriptions.add(onStoreChange)\n\n    return () => {\n      this.subscriptions.delete(onStoreChange)\n    }\n  }\n\n  static compareOptions(a: UseEditorOptions, b: UseEditorOptions) {\n    return (Object.keys(a) as (keyof UseEditorOptions)[]).every(key => {\n      if (\n        [\n          'onCreate',\n          'onBeforeCreate',\n          'onDestroy',\n          'onUpdate',\n          'onTransaction',\n          'onFocus',\n          'onBlur',\n          'onSelectionUpdate',\n          'onContentError',\n          'onDrop',\n          'onPaste',\n        ].includes(key)\n      ) {\n        // we don't want to compare callbacks, they are always different and only registered once\n        return true\n      }\n\n      // We often encourage putting extensions inlined in the options object, so we will do a slightly deeper comparison here\n      if (key === 'extensions' && a.extensions && b.extensions) {\n        if (a.extensions.length !== b.extensions.length) {\n          return false\n        }\n        return a.extensions.every((extension, index) => {\n          if (extension !== b.extensions?.[index]) {\n            return false\n          }\n          return true\n        })\n      }\n      if (a[key] !== b[key]) {\n        // if any of the options have changed, we should update the editor options\n        return false\n      }\n      return true\n    })\n  }\n\n  /**\n   * On each render, we will create, update, or destroy the editor instance.\n   * @param deps The dependencies to watch for changes\n   * @returns A cleanup function\n   */\n  onRender(deps: DependencyList) {\n    // The returned callback will run on each render\n    return () => {\n      this.isComponentMounted = true\n      // Cleanup any scheduled destructions, since we are currently rendering\n      clearTimeout(this.scheduledDestructionTimeout)\n\n      if (this.editor && !this.editor.isDestroyed && deps.length === 0) {\n        // if the editor does exist & deps are empty, we don't need to re-initialize the editor generally\n        if (!EditorInstanceManager.compareOptions(this.options.current, this.editor.options)) {\n          // But, the options are different, so we need to update the editor options\n          // Still, this is faster than re-creating the editor\n          this.editor.setOptions({\n            ...this.options.current,\n            editable: this.editor.isEditable,\n          })\n        }\n      } else {\n        // When the editor:\n        // - does not yet exist\n        // - is destroyed\n        // - the deps array changes\n        // We need to destroy the editor instance and re-initialize it\n        this.refreshEditorInstance(deps)\n      }\n\n      return () => {\n        this.isComponentMounted = false\n        this.scheduleDestroy()\n      }\n    }\n  }\n\n  /**\n   * Recreate the editor instance if the dependencies have changed.\n   */\n  private refreshEditorInstance(deps: DependencyList) {\n    if (this.editor && !this.editor.isDestroyed) {\n      // Editor instance already exists\n      if (this.previousDeps === null) {\n        // If lastDeps has not yet been initialized, reuse the current editor instance\n        this.previousDeps = deps\n        return\n      }\n      const depsAreEqual =\n        this.previousDeps.length === deps.length && this.previousDeps.every((dep, index) => dep === deps[index])\n\n      if (depsAreEqual) {\n        // deps exist and are equal, no need to recreate\n        return\n      }\n    }\n\n    if (this.editor && !this.editor.isDestroyed) {\n      // Destroy the editor instance if it exists\n      this.editor.destroy()\n    }\n\n    this.setEditor(this.createEditor())\n\n    // Update the lastDeps to the current deps\n    this.previousDeps = deps\n  }\n\n  /**\n   * Schedule the destruction of the editor instance.\n   * This will only destroy the editor if it was not mounted on the next tick.\n   * This is to avoid destroying the editor instance when it's actually still mounted.\n   */\n  private scheduleDestroy() {\n    const currentInstanceId = this.instanceId\n    const currentEditor = this.editor\n\n    // Wait two ticks to see if the component is still mounted\n    this.scheduledDestructionTimeout = setTimeout(() => {\n      if (this.isComponentMounted && this.instanceId === currentInstanceId) {\n        // If still mounted on the following tick, with the same instanceId, do not destroy the editor\n        if (currentEditor) {\n          // just re-apply options as they might have changed\n          currentEditor.setOptions(this.options.current)\n        }\n        return\n      }\n      if (currentEditor && !currentEditor.isDestroyed) {\n        currentEditor.destroy()\n        if (this.instanceId === currentInstanceId) {\n          this.setEditor(null)\n        }\n      }\n      // This allows the effect to run again between ticks\n      // which may save us from having to re-create the editor\n    }, 1)\n  }\n}\n\n/**\n * This hook allows you to create an editor instance.\n * @param options The editor options\n * @param deps The dependencies to watch for changes\n * @returns The editor instance\n * @example const editor = useEditor({ extensions: [...] })\n */\nexport function useEditor(\n  options: UseEditorOptions & { immediatelyRender: false },\n  deps?: DependencyList,\n): Editor | null\n\n/**\n * This hook allows you to create an editor instance.\n * @param options The editor options\n * @param deps The dependencies to watch for changes\n * @returns The editor instance\n * @example const editor = useEditor({ extensions: [...] })\n */\nexport function useEditor(options: UseEditorOptions, deps?: DependencyList): Editor\n\nexport function useEditor(options: UseEditorOptions = {}, deps: DependencyList = []): Editor | null {\n  const mostRecentOptions = useRef(options)\n\n  mostRecentOptions.current = options\n\n  const [instanceManager] = useState(() => new EditorInstanceManager(mostRecentOptions))\n\n  const editor = useSyncExternalStore(\n    instanceManager.subscribe,\n    instanceManager.getEditor,\n    instanceManager.getServerSnapshot,\n  )\n\n  useDebugValue(editor)\n\n  // This effect will handle creating/updating the editor instance\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useEffect(instanceManager.onRender(deps))\n\n  // The default behavior is to re-render on each transaction\n  // This is legacy behavior that will be removed in future versions\n  useEditorState({\n    editor,\n    selector: ({ transactionNumber }) => {\n      if (options.shouldRerenderOnTransaction === false || options.shouldRerenderOnTransaction === undefined) {\n        // This will prevent the editor from re-rendering on each transaction\n        return null\n      }\n\n      // This will avoid re-rendering on the first transaction when `immediatelyRender` is set to `true`\n      if (options.immediatelyRender && transactionNumber === 0) {\n        return 0\n      }\n      return transactionNumber + 1\n    },\n  })\n\n  return editor\n}\n", "import type { Editor } from '@tiptap/core'\nimport deepEqual from 'fast-deep-equal/es6/react.js'\nimport { useDebugValue, useEffect, useLayoutEffect, useState } from 'react'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector.js'\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect\n\nexport type EditorStateSnapshot<TEditor extends Editor | null = Editor | null> = {\n  editor: TEditor\n  transactionNumber: number\n}\n\nexport type UseEditorStateOptions<TSelectorResult, TEditor extends Editor | null = Editor | null> = {\n  /**\n   * The editor instance.\n   */\n  editor: TEditor\n  /**\n   * A selector function to determine the value to compare for re-rendering.\n   */\n  selector: (context: EditorStateSnapshot<TEditor>) => TSelectorResult\n  /**\n   * A custom equality function to determine if the editor should re-render.\n   * @default `deepEqual` from `fast-deep-equal`\n   */\n  equalityFn?: (a: TSele<PERSON><PERSON><PERSON>ult, b: TSelectorResult | null) => boolean\n}\n\n/**\n * To synchronize the editor instance with the component state,\n * we need to create a separate instance that is not affected by the component re-renders.\n */\nclass EditorStateManager<TEditor extends Editor | null = Editor | null> {\n  private transactionNumber = 0\n\n  private lastTransactionNumber = 0\n\n  private lastSnapshot: EditorStateSnapshot<TEditor>\n\n  private editor: TEditor\n\n  private subscribers = new Set<() => void>()\n\n  constructor(initialEditor: TEditor) {\n    this.editor = initialEditor\n    this.lastSnapshot = { editor: initialEditor, transactionNumber: 0 }\n\n    this.getSnapshot = this.getSnapshot.bind(this)\n    this.getServerSnapshot = this.getServerSnapshot.bind(this)\n    this.watch = this.watch.bind(this)\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  /**\n   * Get the current editor instance.\n   */\n  getSnapshot(): EditorStateSnapshot<TEditor> {\n    if (this.transactionNumber === this.lastTransactionNumber) {\n      return this.lastSnapshot\n    }\n    this.lastTransactionNumber = this.transactionNumber\n    this.lastSnapshot = { editor: this.editor, transactionNumber: this.transactionNumber }\n    return this.lastSnapshot\n  }\n\n  /**\n   * Always disable the editor on the server-side.\n   */\n  getServerSnapshot(): EditorStateSnapshot<null> {\n    return { editor: null, transactionNumber: 0 }\n  }\n\n  /**\n   * Subscribe to the editor instance's changes.\n   */\n  subscribe(callback: () => void): () => void {\n    this.subscribers.add(callback)\n    return () => {\n      this.subscribers.delete(callback)\n    }\n  }\n\n  /**\n   * Watch the editor instance for changes.\n   */\n  watch(nextEditor: Editor | null): undefined | (() => void) {\n    this.editor = nextEditor as TEditor\n\n    if (this.editor) {\n      /**\n       * This will force a re-render when the editor state changes.\n       * This is to support things like `editor.can().toggleBold()` in components that `useEditor`.\n       * This could be more efficient, but it's a good trade-off for now.\n       */\n      const fn = () => {\n        this.transactionNumber += 1\n        this.subscribers.forEach(callback => callback())\n      }\n\n      const currentEditor = this.editor\n\n      currentEditor.on('transaction', fn)\n      return () => {\n        currentEditor.off('transaction', fn)\n      }\n    }\n\n    return undefined\n  }\n}\n\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor>,\n): TSelectorResult\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor | null>,\n): TSelectorResult | null\n\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor> | UseEditorStateOptions<TSelectorResult, Editor | null>,\n): TSelectorResult | null {\n  const [editorStateManager] = useState(() => new EditorStateManager(options.editor))\n\n  // Using the `useSyncExternalStore` hook to sync the editor instance with the component state\n  const selectedState = useSyncExternalStoreWithSelector(\n    editorStateManager.subscribe,\n    editorStateManager.getSnapshot,\n    editorStateManager.getServerSnapshot,\n    options.selector as UseEditorStateOptions<TSelectorResult, Editor | null>['selector'],\n    options.equalityFn ?? deepEqual,\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    return editorStateManager.watch(options.editor)\n  }, [options.editor, editorStateManager])\n\n  useDebugValue(selectedState)\n\n  return selectedState\n}\n", "import type { ReactNode } from 'react'\nimport { createContext, createElement, useContext } from 'react'\n\nexport interface ReactNodeViewContextProps {\n  onDragStart?: (event: DragEvent) => void\n  nodeViewContentRef?: (element: HTMLElement | null) => void\n  /**\n   * This allows you to add children into the NodeViewContent component.\n   * This is useful when statically rendering the content of a node view.\n   */\n  nodeViewContentChildren?: ReactNode\n}\n\nexport const ReactNodeViewContext = createContext<ReactNodeViewContextProps>({\n  onDragStart: () => {\n    // no-op\n  },\n  nodeViewContentChildren: undefined,\n  nodeViewContentRef: () => {\n    // no-op\n  },\n})\n\nexport const ReactNodeViewContentProvider = ({ children, content }: { children: ReactNode; content: ReactNode }) => {\n  return createElement(ReactNodeViewContext.Provider, { value: { nodeViewContentChildren: content } }, children)\n}\n\nexport const useReactNodeView = () => useContext(ReactNodeViewContext)\n", "import type { ComponentProps } from 'react'\nimport React from 'react'\n\nimport { useReactNodeView } from './useReactNodeView.js'\n\nexport type NodeViewContentProps<T extends keyof React.JSX.IntrinsicElements = 'div'> = {\n  as?: NoInfer<T>\n} & ComponentProps<T>\n\nexport function NodeViewContent<T extends keyof React.JSX.IntrinsicElements = 'div'>({\n  as: Tag = 'div' as T,\n  ...props\n}: NodeViewContentProps<T>) {\n  const { nodeViewContentRef, nodeViewContentChildren } = useReactNodeView()\n\n  return (\n    // @ts-ignore\n    <Tag\n      {...props}\n      ref={nodeViewContentRef}\n      data-node-view-content=\"\"\n      style={{\n        whiteSpace: 'pre-wrap',\n        ...props.style,\n      }}\n    >\n      {nodeViewContentChildren}\n    </Tag>\n  )\n}\n", "import React from 'react'\n\nimport { useReactNodeView } from './useReactNodeView.js'\n\nexport interface NodeViewWrapperProps {\n  [key: string]: any\n  as?: React.ElementType\n}\n\nexport const NodeViewWrapper: React.FC<NodeViewWrapperProps> = React.forwardRef((props, ref) => {\n  const { onDragStart } = useReactNodeView()\n  const Tag = props.as || 'div'\n\n  return (\n    // @ts-ignore\n    <Tag\n      {...props}\n      ref={ref}\n      data-node-view-wrapper=\"\"\n      onDragStart={onDragStart}\n      style={{\n        whiteSpace: 'normal',\n        ...props.style,\n      }}\n    />\n  )\n})\n", "/* eslint-disable @typescript-eslint/no-shadow */\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>w<PERSON>endererOptions } from '@tiptap/core'\nimport { MarkView } from '@tiptap/core'\nimport React from 'react'\n\n// import { flushSync } from 'react-dom'\nimport { ReactRenderer } from './ReactRenderer.js'\n\nexport interface MarkViewContextProps {\n  markViewContentRef: (element: HTMLElement | null) => void\n}\nexport const ReactMarkViewContext = React.createContext<MarkViewContextProps>({\n  markViewContentRef: () => {\n    // do nothing\n  },\n})\n\nexport type MarkViewContentProps<T extends keyof React.JSX.IntrinsicElements = 'span'> = {\n  as?: T\n} & Omit<React.ComponentProps<T>, 'as'>\n\nexport const MarkViewContent = <T extends keyof React.JSX.IntrinsicElements = 'span'>(\n  props: MarkViewContentProps<T>,\n) => {\n  const { as: Tag = 'span', ...rest } = props\n  const { markViewContentRef } = React.useContext(ReactMarkViewContext)\n\n  return (\n    // @ts-ignore\n    <Tag {...rest} ref={markViewContentRef} data-mark-view-content=\"\" />\n  )\n}\n\nexport interface ReactMarkViewRendererOptions extends MarkViewRendererOptions {\n  /**\n   * The tag name of the element wrapping the React component.\n   */\n  as?: string\n  className?: string\n  attrs?: { [key: string]: string }\n}\n\nexport class ReactMarkView extends MarkView<React.ComponentType<MarkViewProps>, ReactMarkViewRendererOptions> {\n  renderer: ReactRenderer\n  contentDOMElement: HTMLElement | null\n  didMountContentDomElement = false\n\n  constructor(\n    component: React.ComponentType<MarkViewProps>,\n    props: MarkViewProps,\n    options?: Partial<ReactMarkViewRendererOptions>,\n  ) {\n    super(component, props, options)\n\n    const { as = 'span', attrs, className = '' } = options || {}\n    const componentProps = { ...props, updateAttributes: this.updateAttributes.bind(this) } satisfies MarkViewProps\n\n    this.contentDOMElement = document.createElement('span')\n\n    const markViewContentRef: MarkViewContextProps['markViewContentRef'] = el => {\n      if (el && this.contentDOMElement && el.firstChild !== this.contentDOMElement) {\n        el.appendChild(this.contentDOMElement)\n        this.didMountContentDomElement = true\n      }\n    }\n    const context: MarkViewContextProps = {\n      markViewContentRef,\n    }\n\n    // For performance reasons, we memoize the provider component\n    // And all of the things it requires are declared outside of the component, so it doesn't need to re-render\n    const ReactMarkViewProvider: React.FunctionComponent<MarkViewProps> = React.memo(componentProps => {\n      return (\n        <ReactMarkViewContext.Provider value={context}>\n          {React.createElement(component, componentProps)}\n        </ReactMarkViewContext.Provider>\n      )\n    })\n\n    ReactMarkViewProvider.displayName = 'ReactNodeView'\n\n    this.renderer = new ReactRenderer(ReactMarkViewProvider, {\n      editor: props.editor,\n      props: componentProps,\n      as,\n      className: `mark-${props.mark.type.name} ${className}`.trim(),\n    })\n\n    if (attrs) {\n      this.renderer.updateAttributes(attrs)\n    }\n  }\n\n  get dom() {\n    return this.renderer.element as HTMLElement\n  }\n\n  get contentDOM() {\n    if (!this.didMountContentDomElement) {\n      return null\n    }\n    return this.contentDOMElement as HTMLElement\n  }\n}\n\nexport function ReactMarkViewRenderer(\n  component: React.ComponentType<MarkViewProps>,\n  options: Partial<ReactMarkViewRendererOptions> = {},\n): MarkViewRenderer {\n  return props => new ReactMarkView(component, props, options)\n}\n", "import type { Editor } from '@tiptap/core'\nimport type {\n  ComponentClass,\n  ForwardRefExoticComponent,\n  FunctionComponent,\n  PropsWithoutRef,\n  ReactNode,\n  RefAttributes,\n} from 'react'\nimport { version as reactVersion } from 'react'\nimport { flushSync } from 'react-dom'\n\nimport type { EditorWithContentComponent } from './Editor.js'\n\n/**\n * Check if a component is a class component.\n * @param Component\n * @returns {boolean}\n */\nfunction isClassComponent(Component: any) {\n  return !!(typeof Component === 'function' && Component.prototype && Component.prototype.isReactComponent)\n}\n\n/**\n * Check if a component is a forward ref component.\n * @param Component\n * @returns {boolean}\n */\nfunction isForwardRefComponent(Component: any) {\n  return !!(\n    typeof Component === 'object' &&\n    Component.$$typeof &&\n    (Component.$$typeof.toString() === 'Symbol(react.forward_ref)' ||\n      Component.$$typeof.description === 'react.forward_ref')\n  )\n}\n\n/**\n * Check if a component is a memoized component.\n * @param Component\n * @returns {boolean}\n */\nfunction isMemoComponent(Component: any) {\n  return !!(\n    typeof Component === 'object' &&\n    Component.$$typeof &&\n    (Component.$$typeof.toString() === 'Symbol(react.memo)' || Component.$$typeof.description === 'react.memo')\n  )\n}\n\n/**\n * Check if a component can safely receive a ref prop.\n * This includes class components, forwardRef components, and memoized components\n * that wrap forwardRef or class components.\n * @param Component\n * @returns {boolean}\n */\nfunction canReceiveRef(Component: any) {\n  // Check if it's a class component\n  if (isClassComponent(Component)) {\n    return true\n  }\n\n  // Check if it's a forwardRef component\n  if (isForwardRefComponent(Component)) {\n    return true\n  }\n\n  // Check if it's a memoized component\n  if (isMemoComponent(Component)) {\n    // For memoized components, check the wrapped component\n    const wrappedComponent = Component.type\n    if (wrappedComponent) {\n      return isClassComponent(wrappedComponent) || isForwardRefComponent(wrappedComponent)\n    }\n  }\n\n  return false\n}\n\n/**\n * Check if we're running React 19+ by detecting if function components support ref props\n * @returns {boolean}\n */\nfunction isReact19Plus(): boolean {\n  // React 19 is detected by checking React version if available\n  // In practice, we'll use a more conservative approach and assume React 18 behavior\n  // unless we can definitively detect React 19\n  try {\n    // @ts-ignore\n    if (reactVersion) {\n      const majorVersion = parseInt(reactVersion.split('.')[0], 10)\n      return majorVersion >= 19\n    }\n  } catch {\n    // Fallback to React 18 behavior if we can't determine version\n  }\n  return false\n}\n\nexport interface ReactRendererOptions {\n  /**\n   * The editor instance.\n   * @type {Editor}\n   */\n  editor: Editor\n\n  /**\n   * The props for the component.\n   * @type {Record<string, any>}\n   * @default {}\n   */\n  props?: Record<string, any>\n\n  /**\n   * The tag name of the element.\n   * @type {string}\n   * @default 'div'\n   */\n  as?: string\n\n  /**\n   * The class name of the element.\n   * @type {string}\n   * @default ''\n   * @example 'foo bar'\n   */\n  className?: string\n}\n\ntype ComponentType<R, P> =\n  | ComponentClass<P>\n  | FunctionComponent<P>\n  | ForwardRefExoticComponent<PropsWithoutRef<P> & RefAttributes<R>>\n\n/**\n * The ReactRenderer class. It's responsible for rendering React components inside the editor.\n * @example\n * new ReactRenderer(MyComponent, {\n *   editor,\n *   props: {\n *     foo: 'bar',\n *   },\n *   as: 'span',\n * })\n */\nexport class ReactRenderer<R = unknown, P extends Record<string, any> = object> {\n  id: string\n\n  editor: Editor\n\n  component: any\n\n  element: Element\n\n  props: P\n\n  reactElement: ReactNode\n\n  ref: R | null = null\n\n  /**\n   * Immediately creates element and renders the provided React component.\n   */\n  constructor(\n    component: ComponentType<R, P>,\n    { editor, props = {}, as = 'div', className = '' }: ReactRendererOptions,\n  ) {\n    this.id = Math.floor(Math.random() * 0xffffffff).toString()\n    this.component = component\n    this.editor = editor as EditorWithContentComponent\n    this.props = props as P\n    this.element = document.createElement(as)\n    this.element.classList.add('react-renderer')\n\n    if (className) {\n      this.element.classList.add(...className.split(' '))\n    }\n\n    // If the editor is already initialized, we will need to\n    // synchronously render the component to ensure it renders\n    // together with Prosemirror's rendering.\n    if (this.editor.isInitialized) {\n      flushSync(() => {\n        this.render()\n      })\n    } else {\n      queueMicrotask(() => {\n        this.render()\n      })\n    }\n  }\n\n  /**\n   * Render the React component.\n   */\n  render(): void {\n    const Component = this.component\n    const props = this.props\n    const editor = this.editor as EditorWithContentComponent\n\n    // Handle ref forwarding with React 18/19 compatibility\n    const isReact19 = isReact19Plus()\n    const componentCanReceiveRef = canReceiveRef(Component)\n\n    const elementProps = { ...props }\n\n    // Always remove ref if the component cannot receive it (unless React 19+)\n    if (elementProps.ref && !(isReact19 || componentCanReceiveRef)) {\n      delete elementProps.ref\n    }\n\n    // Only assign our own ref if allowed\n    if (!elementProps.ref && (isReact19 || componentCanReceiveRef)) {\n      // @ts-ignore - Setting ref prop for compatible components\n      elementProps.ref = (ref: R) => {\n        this.ref = ref\n      }\n    }\n\n    this.reactElement = <Component {...elementProps} />\n\n    editor?.contentComponent?.setRenderer(this.id, this)\n  }\n\n  /**\n   * Re-renders the React component with new props.\n   */\n  updateProps(props: Record<string, any> = {}): void {\n    this.props = {\n      ...this.props,\n      ...props,\n    }\n\n    this.render()\n  }\n\n  /**\n   * Destroy the React component.\n   */\n  destroy(): void {\n    const editor = this.editor as EditorWithContentComponent\n\n    editor?.contentComponent?.removeRenderer(this.id)\n  }\n\n  /**\n   * Update the attributes of the element that holds the React component.\n   */\n  updateAttributes(attributes: Record<string, string>): void {\n    Object.keys(attributes).forEach(key => {\n      this.element.setAttribute(key, attributes[key])\n    })\n  }\n}\n", "import type {\n  DecorationWithType,\n  Editor,\n  NodeViewRenderer,\n  NodeViewRendererOptions,\n  NodeViewRendererProps,\n} from '@tiptap/core'\nimport { getRenderedAttributes, NodeView } from '@tiptap/core'\nimport type { Node, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport type { Decoration, DecorationSource, NodeView as ProseMirrorNodeView } from '@tiptap/pm/view'\nimport type { ComponentType, NamedExoticComponent } from 'react'\nimport { createElement, createRef, memo } from 'react'\n\nimport type { EditorWithContentComponent } from './Editor.js'\nimport { ReactRenderer } from './ReactRenderer.js'\nimport type { ReactNodeViewProps } from './types.js'\nimport type { ReactNodeViewContextProps } from './useReactNodeView.js'\nimport { ReactNodeViewContext } from './useReactNodeView.js'\n\nexport interface ReactNodeViewRendererOptions extends NodeViewRendererOptions {\n  /**\n   * This function is called when the node view is updated.\n   * It allows you to compare the old node with the new node and decide if the component should update.\n   */\n  update:\n    | ((props: {\n        oldNode: ProseMirrorNode\n        oldDecorations: readonly Decoration[]\n        oldInnerDecorations: DecorationSource\n        newNode: ProseMirrorNode\n        newDecorations: readonly Decoration[]\n        innerDecorations: DecorationSource\n        updateProps: () => void\n      }) => boolean)\n    | null\n  /**\n   * The tag name of the element wrapping the React component.\n   */\n  as?: string\n  /**\n   * The class name of the element wrapping the React component.\n   */\n  className?: string\n  /**\n   * Attributes that should be applied to the element wrapping the React component.\n   * If this is a function, it will be called each time the node view is updated.\n   * If this is an object, it will be applied once when the node view is mounted.\n   */\n  attrs?:\n    | Record<string, string>\n    | ((props: { node: ProseMirrorNode; HTMLAttributes: Record<string, any> }) => Record<string, string>)\n}\n\nexport class ReactNodeView<\n  T = HTMLElement,\n  Component extends ComponentType<ReactNodeViewProps<T>> = ComponentType<ReactNodeViewProps<T>>,\n  NodeEditor extends Editor = Editor,\n  Options extends ReactNodeViewRendererOptions = ReactNodeViewRendererOptions,\n> extends NodeView<Component, NodeEditor, Options> {\n  /**\n   * The renderer instance.\n   */\n  renderer!: ReactRenderer<unknown, ReactNodeViewProps<T>>\n\n  /**\n   * The element that holds the rich-text content of the node.\n   */\n  contentDOMElement!: HTMLElement | null\n\n  constructor(component: Component, props: NodeViewRendererProps, options?: Partial<Options>) {\n    super(component, props, options)\n\n    if (!this.node.isLeaf) {\n      if (this.options.contentDOMElementTag) {\n        this.contentDOMElement = document.createElement(this.options.contentDOMElementTag)\n      } else {\n        this.contentDOMElement = document.createElement(this.node.isInline ? 'span' : 'div')\n      }\n\n      this.contentDOMElement.dataset.nodeViewContentReact = ''\n      this.contentDOMElement.dataset.nodeViewWrapper = ''\n\n      // For some reason the whiteSpace prop is not inherited properly in Chrome and Safari\n      // With this fix it seems to work fine\n      // See: https://github.com/ueberdosis/tiptap/issues/1197\n      this.contentDOMElement.style.whiteSpace = 'inherit'\n\n      const contentTarget = this.dom.querySelector('[data-node-view-content]')\n\n      if (!contentTarget) {\n        return\n      }\n\n      contentTarget.appendChild(this.contentDOMElement)\n    }\n  }\n\n  /**\n   * Setup the React component.\n   * Called on initialization.\n   */\n  mount() {\n    const props = {\n      editor: this.editor,\n      node: this.node,\n      decorations: this.decorations as DecorationWithType[],\n      innerDecorations: this.innerDecorations,\n      view: this.view,\n      selected: false,\n      extension: this.extension,\n      HTMLAttributes: this.HTMLAttributes,\n      getPos: () => this.getPos(),\n      updateAttributes: (attributes = {}) => this.updateAttributes(attributes),\n      deleteNode: () => this.deleteNode(),\n      ref: createRef<T>(),\n    } satisfies ReactNodeViewProps<T>\n\n    if (!(this.component as any).displayName) {\n      const capitalizeFirstChar = (string: string): string => {\n        return string.charAt(0).toUpperCase() + string.substring(1)\n      }\n\n      this.component.displayName = capitalizeFirstChar(this.extension.name)\n    }\n\n    const onDragStart = this.onDragStart.bind(this)\n    const nodeViewContentRef: ReactNodeViewContextProps['nodeViewContentRef'] = element => {\n      if (element && this.contentDOMElement && element.firstChild !== this.contentDOMElement) {\n        // remove the nodeViewWrapper attribute from the element\n        if (element.hasAttribute('data-node-view-wrapper')) {\n          element.removeAttribute('data-node-view-wrapper')\n        }\n        element.appendChild(this.contentDOMElement)\n      }\n    }\n    const context = { onDragStart, nodeViewContentRef }\n    const Component = this.component\n    // For performance reasons, we memoize the provider component\n    // And all of the things it requires are declared outside of the component, so it doesn't need to re-render\n    const ReactNodeViewProvider: NamedExoticComponent<ReactNodeViewProps<T>> = memo(componentProps => {\n      return (\n        <ReactNodeViewContext.Provider value={context}>\n          {createElement(Component, componentProps)}\n        </ReactNodeViewContext.Provider>\n      )\n    })\n\n    ReactNodeViewProvider.displayName = 'ReactNodeView'\n\n    let as = this.node.isInline ? 'span' : 'div'\n\n    if (this.options.as) {\n      as = this.options.as\n    }\n\n    const { className = '' } = this.options\n\n    this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this)\n\n    this.renderer = new ReactRenderer(ReactNodeViewProvider, {\n      editor: this.editor,\n      props,\n      as,\n      className: `node-${this.node.type.name} ${className}`.trim(),\n    })\n\n    this.editor.on('selectionUpdate', this.handleSelectionUpdate)\n    this.updateElementAttributes()\n  }\n\n  /**\n   * Return the DOM element.\n   * This is the element that will be used to display the node view.\n   */\n  get dom() {\n    if (\n      this.renderer.element.firstElementChild &&\n      !this.renderer.element.firstElementChild?.hasAttribute('data-node-view-wrapper')\n    ) {\n      throw Error('Please use the NodeViewWrapper component for your node view.')\n    }\n\n    return this.renderer.element as HTMLElement\n  }\n\n  /**\n   * Return the content DOM element.\n   * This is the element that will be used to display the rich-text content of the node.\n   */\n  get contentDOM() {\n    if (this.node.isLeaf) {\n      return null\n    }\n\n    return this.contentDOMElement\n  }\n\n  /**\n   * On editor selection update, check if the node is selected.\n   * If it is, call `selectNode`, otherwise call `deselectNode`.\n   */\n  handleSelectionUpdate() {\n    const { from, to } = this.editor.state.selection\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n\n    if (from <= pos && to >= pos + this.node.nodeSize) {\n      if (this.renderer.props.selected) {\n        return\n      }\n\n      this.selectNode()\n    } else {\n      if (!this.renderer.props.selected) {\n        return\n      }\n\n      this.deselectNode()\n    }\n  }\n\n  /**\n   * On update, update the React component.\n   * To prevent unnecessary updates, the `update` option can be used.\n   */\n  update(node: Node, decorations: readonly Decoration[], innerDecorations: DecorationSource): boolean {\n    const rerenderComponent = (props?: Record<string, any>) => {\n      this.renderer.updateProps(props)\n      if (typeof this.options.attrs === 'function') {\n        this.updateElementAttributes()\n      }\n    }\n\n    if (node.type !== this.node.type) {\n      return false\n    }\n\n    if (typeof this.options.update === 'function') {\n      const oldNode = this.node\n      const oldDecorations = this.decorations\n      const oldInnerDecorations = this.innerDecorations\n\n      this.node = node\n      this.decorations = decorations\n      this.innerDecorations = innerDecorations\n\n      return this.options.update({\n        oldNode,\n        oldDecorations,\n        newNode: node,\n        newDecorations: decorations,\n        oldInnerDecorations,\n        innerDecorations,\n        updateProps: () => rerenderComponent({ node, decorations, innerDecorations }),\n      })\n    }\n\n    if (node === this.node && this.decorations === decorations && this.innerDecorations === innerDecorations) {\n      return true\n    }\n\n    this.node = node\n    this.decorations = decorations\n    this.innerDecorations = innerDecorations\n\n    rerenderComponent({ node, decorations, innerDecorations })\n\n    return true\n  }\n\n  /**\n   * Select the node.\n   * Add the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  selectNode() {\n    this.renderer.updateProps({\n      selected: true,\n    })\n    this.renderer.element.classList.add('ProseMirror-selectednode')\n  }\n\n  /**\n   * Deselect the node.\n   * Remove the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  deselectNode() {\n    this.renderer.updateProps({\n      selected: false,\n    })\n    this.renderer.element.classList.remove('ProseMirror-selectednode')\n  }\n\n  /**\n   * Destroy the React component instance.\n   */\n  destroy() {\n    this.renderer.destroy()\n    this.editor.off('selectionUpdate', this.handleSelectionUpdate)\n    this.contentDOMElement = null\n  }\n\n  /**\n   * Update the attributes of the top-level element that holds the React component.\n   * Applying the attributes defined in the `attrs` option.\n   */\n  updateElementAttributes() {\n    if (this.options.attrs) {\n      let attrsObj: Record<string, string> = {}\n\n      if (typeof this.options.attrs === 'function') {\n        const extensionAttributes = this.editor.extensionManager.attributes\n        const HTMLAttributes = getRenderedAttributes(this.node, extensionAttributes)\n\n        attrsObj = this.options.attrs({ node: this.node, HTMLAttributes })\n      } else {\n        attrsObj = this.options.attrs\n      }\n\n      this.renderer.updateAttributes(attrsObj)\n    }\n  }\n}\n\n/**\n * Create a React node view renderer.\n */\nexport function ReactNodeViewRenderer<T = HTMLElement>(\n  component: ComponentType<ReactNodeViewProps<T>>,\n  options?: Partial<ReactNodeViewRendererOptions>,\n): NodeViewRenderer {\n  return props => {\n    // try to get the parent component\n    // this is important for vue devtools to show the component hierarchy correctly\n    // maybe it’s `undefined` because <editor-content> isn’t rendered yet\n    if (!(props.editor as EditorWithContentComponent).contentComponent) {\n      return {} as unknown as ProseMirrorNodeView\n    }\n\n    return new ReactNodeView<T>(component, props, options)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACEA,IAAAA,gBAA0D;;;ACA1D,mBAAkC;AAClC,uBAAqB;AACrB,kBAAqC;AA6B5B;AAxBT,IAAM,YAAY,IAA8B,SAAgE;AAC9G,SAAO,CAAC,SAAY;AAClB,SAAK,QAAQ,SAAO;AAClB,UAAI,OAAO,QAAQ,YAAY;AAC7B,YAAI,IAAI;AAAA,MACV,WAAW,KAAK;AACd;AAAC,QAAC,IAAmC,UAAU;AAAA,MACjD;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAKA,IAAM,UAA4D,CAAC,EAAE,iBAAiB,MAAM;AAE1F,QAAM,gBAAY;AAAA,IAChB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EACnB;AAGA,SAAO,2EAAG,iBAAO,OAAO,SAAS,GAAE;AACrC;AAOA,SAAS,cAAgC;AACvC,QAAM,cAAc,oBAAI,IAAgB;AACxC,MAAI,YAA+C,CAAC;AAEpD,SAAO;AAAA;AAAA;AAAA;AAAA,IAIL,UAAU,UAAsB;AAC9B,kBAAY,IAAI,QAAQ;AACxB,aAAO,MAAM;AACX,oBAAY,OAAO,QAAQ;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,cAAc;AACZ,aAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAClB,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA,IAIA,YAAY,IAAY,UAAyB;AAC/C,kBAAY;AAAA,QACV,GAAG;AAAA,QACH,CAAC,EAAE,GAAG,iBAAAC,QAAS,aAAa,SAAS,cAAc,SAAS,SAAS,EAAE;AAAA,MACzE;AAEA,kBAAY,QAAQ,gBAAc,WAAW,CAAC;AAAA,IAChD;AAAA;AAAA;AAAA;AAAA,IAIA,eAAe,IAAY;AACzB,YAAM,gBAAgB,EAAE,GAAG,UAAU;AAErC,aAAO,cAAc,EAAE;AACvB,kBAAY;AACZ,kBAAY,QAAQ,gBAAc,WAAW,CAAC;AAAA,IAChD;AAAA,EACF;AACF;AAEO,IAAM,oBAAN,cAAgC,aAAAC,QAAM,UAG3C;AAAA,EAOA,YAAY,OAA2B;AA/FzC;AAgGI,UAAM,KAAK;AACX,SAAK,mBAAmB,aAAAA,QAAM,UAAU;AACxC,SAAK,cAAc;AAEnB,SAAK,QAAQ;AAAA,MACX,gCAAgC,SAAS,WAAM,WAAN,mBAAoD,gBAAgB;AAAA,IAC/G;AAAA,EACF;AAAA,EAEA,oBAAoB;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EAEA,qBAAqB;AACnB,SAAK,KAAK;AAAA,EACZ;AAAA,EAEA,OAAO;AACL,UAAM,SAAS,KAAK,MAAM;AAE1B,QAAI,UAAU,CAAC,OAAO,eAAe,OAAO,QAAQ,SAAS;AAC3D,UAAI,OAAO,kBAAkB;AAC3B;AAAA,MACF;AAEA,YAAM,UAAU,KAAK,iBAAiB;AAEtC,cAAQ,OAAO,GAAG,OAAO,QAAQ,QAAQ,UAAU;AAEnD,aAAO,WAAW;AAAA,QAChB;AAAA,MACF,CAAC;AAED,aAAO,mBAAmB,YAAY;AAGtC,UAAI,CAAC,KAAK,MAAM,gCAAgC;AAE9C,aAAK,gCAAgC,OAAO,iBAAiB,UAAU,MAAM;AAC3E,eAAK,SAAS,eAAa;AACzB,gBAAI,CAAC,UAAU,gCAAgC;AAC7C,qBAAO;AAAA,gBACL,gCAAgC;AAAA,cAClC;AAAA,YACF;AACA,mBAAO;AAAA,UACT,CAAC;AAGD,cAAI,KAAK,+BAA+B;AACtC,iBAAK,8BAA8B;AAAA,UACrC;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO,gBAAgB;AAEvB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,uBAAuB;AA7JzB;AA8JI,UAAM,SAAS,KAAK,MAAM;AAE1B,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AAEA,SAAK,cAAc;AAEnB,QAAI,CAAC,OAAO,aAAa;AACvB,aAAO,KAAK,SAAS;AAAA,QACnB,WAAW,CAAC;AAAA,MACd,CAAC;AAAA,IACH;AAEA,QAAI,KAAK,+BAA+B;AACtC,WAAK,8BAA8B;AAAA,IACrC;AAEA,WAAO,mBAAmB;AAE1B,QAAI,GAAC,YAAO,QAAQ,YAAf,mBAAwB,aAAY;AACvC;AAAA,IACF;AAGA,UAAM,aAAa,SAAS,cAAc,KAAK;AAE/C,eAAW,OAAO,GAAG,OAAO,QAAQ,QAAQ,UAAU;AAEtD,WAAO,WAAW;AAAA,MAChB,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA,EAEA,SAAS;AACP,UAAM,EAAE,QAAQ,UAAU,GAAG,KAAK,IAAI,KAAK;AAE3C,WACE,4EACE;AAAA,kDAAC,SAAI,KAAK,UAAU,UAAU,KAAK,gBAAgB,GAAI,GAAG,MAAM;AAAA,OAE/D,iCAAQ,qBAAoB,4CAAC,WAAQ,kBAAkB,OAAO,kBAAkB;AAAA,OACnF;AAAA,EAEJ;AACF;AAGA,IAAM,2BAAuB;AAAA,EAC3B,CAAC,OAA6C,QAAQ;AACpD,UAAM,MAAM,aAAAA,QAAM,QAAQ,MAAM;AAC9B,aAAO,KAAK,MAAM,KAAK,OAAO,IAAI,UAAU,EAAE,SAAS;AAAA,IAEzD,GAAG,CAAC,MAAM,MAAM,CAAC;AAGjB,WAAO,aAAAA,QAAM,cAAc,mBAAmB;AAAA,MAC5C;AAAA,MACA,UAAU;AAAA,MACV,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF;AAEO,IAAM,gBAAgB,aAAAA,QAAM,KAAK,oBAAoB;;;AC9N5D,kBAA2C;AAE3C,IAAAC,gBAA2D;AAC3D,IAAAC,eAAqC;;;ACFrC,IAAAC,gBAAsB;AACtB,IAAAA,gBAAoE;AACpE,2BAAiD;AAEjD,IAAM,4BAA4B,OAAO,WAAW,cAAc,gCAAkB;AA2BpF,IAAM,qBAAN,MAAwE;AAAA,EAWtE,YAAY,eAAwB;AAVpC,SAAQ,oBAAoB;AAE5B,SAAQ,wBAAwB;AAMhC,SAAQ,cAAc,oBAAI,IAAgB;AAGxC,SAAK,SAAS;AACd,SAAK,eAAe,EAAE,QAAQ,eAAe,mBAAmB,EAAE;AAElE,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AACjC,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,cAA4C;AAC1C,QAAI,KAAK,sBAAsB,KAAK,uBAAuB;AACzD,aAAO,KAAK;AAAA,IACd;AACA,SAAK,wBAAwB,KAAK;AAClC,SAAK,eAAe,EAAE,QAAQ,KAAK,QAAQ,mBAAmB,KAAK,kBAAkB;AACrF,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,oBAA+C;AAC7C,WAAO,EAAE,QAAQ,MAAM,mBAAmB,EAAE;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,UAAkC;AAC1C,SAAK,YAAY,IAAI,QAAQ;AAC7B,WAAO,MAAM;AACX,WAAK,YAAY,OAAO,QAAQ;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,YAAqD;AACzD,SAAK,SAAS;AAEd,QAAI,KAAK,QAAQ;AAMf,YAAM,KAAK,MAAM;AACf,aAAK,qBAAqB;AAC1B,aAAK,YAAY,QAAQ,cAAY,SAAS,CAAC;AAAA,MACjD;AAEA,YAAM,gBAAgB,KAAK;AAE3B,oBAAc,GAAG,eAAe,EAAE;AAClC,aAAO,MAAM;AACX,sBAAc,IAAI,eAAe,EAAE;AAAA,MACrC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AA0CO,SAAS,eACd,SACwB;AAzJ1B;AA0JE,QAAM,CAAC,kBAAkB,QAAI,wBAAS,MAAM,IAAI,mBAAmB,QAAQ,MAAM,CAAC;AAGlF,QAAM,oBAAgB;AAAA,IACpB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,QAAQ;AAAA,KACR,aAAQ,eAAR,YAAsB,cAAAC;AAAA,EACxB;AAEA,4BAA0B,MAAM;AAC9B,WAAO,mBAAmB,MAAM,QAAQ,MAAM;AAAA,EAChD,GAAG,CAAC,QAAQ,QAAQ,kBAAkB,CAAC;AAEvC,mCAAc,aAAa;AAE3B,SAAO;AACT;;;ADpKA,IAAM,QAAQ,QAAQ,IAAI,aAAa;AACvC,IAAM,QAAQ,OAAO,WAAW;AAChC,IAAM,SAAS,SAAS,QAAQ,OAAO,WAAW,eAAgB,OAAe,IAAI;AAwBrF,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAqC1B,YAAY,SAA6C;AAjCzD;AAAA;AAAA;AAAA,SAAQ,SAAwB;AAWhC;AAAA;AAAA;AAAA;AAAA,SAAQ,gBAAgB,oBAAI,IAAgB;AAU5C;AAAA;AAAA;AAAA,SAAQ,qBAAqB;AAK7B;AAAA;AAAA;AAAA,SAAQ,eAAsC;AAK9C;AAAA;AAAA;AAAA,SAAO,aAAa;AAGlB,SAAK,UAAU;AACf,SAAK,gBAAgB,oBAAI,IAAgB;AACzC,SAAK,UAAU,KAAK,iBAAiB,CAAC;AACtC,SAAK,gBAAgB;AAErB,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,wBAAwB,KAAK,sBAAsB,KAAK,IAAI;AACjE,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAAA,EACjD;AAAA,EAEQ,UAAU,QAAuB;AACvC,SAAK,SAAS;AACd,SAAK,aAAa,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,GAAG,CAAC;AAGvD,SAAK,cAAc,QAAQ,QAAM,GAAG,CAAC;AAAA,EACvC;AAAA,EAEQ,mBAAmB;AACzB,QAAI,KAAK,QAAQ,QAAQ,sBAAsB,QAAW;AACxD,UAAI,SAAS,QAAQ;AACnB,YAAI,OAAO;AAKT,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAGA,eAAO;AAAA,MACT;AAGA,aAAO,KAAK,aAAa;AAAA,IAC3B;AAEA,QAAI,KAAK,QAAQ,QAAQ,qBAAqB,SAAS,OAAO;AAE5D,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,QAAQ,QAAQ,mBAAmB;AAC1C,aAAO,KAAK,aAAa;AAAA,IAC3B;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKQ,eAAuB;AAC7B,UAAM,iBAAyC;AAAA,MAC7C,GAAG,KAAK,QAAQ;AAAA;AAAA,MAEhB,gBAAgB,IAAI,SAAM;AAxIhC;AAwImC,gCAAK,QAAQ,SAAQ,mBAArB,4BAAsC,GAAG;AAAA;AAAA,MACtE,QAAQ,IAAI,SAAM;AAzIxB;AAyI2B,gCAAK,QAAQ,SAAQ,WAArB,4BAA8B,GAAG;AAAA;AAAA,MACtD,UAAU,IAAI,SAAM;AA1I1B;AA0I6B,gCAAK,QAAQ,SAAQ,aAArB,4BAAgC,GAAG;AAAA;AAAA,MAC1D,WAAW,IAAI,SAAM;AA3I3B;AA2I8B,gCAAK,QAAQ,SAAQ,cAArB,4BAAiC,GAAG;AAAA;AAAA,MAC5D,SAAS,IAAI,SAAM;AA5IzB;AA4I4B,gCAAK,QAAQ,SAAQ,YAArB,4BAA+B,GAAG;AAAA;AAAA,MACxD,mBAAmB,IAAI,SAAM;AA7InC;AA6IsC,gCAAK,QAAQ,SAAQ,sBAArB,4BAAyC,GAAG;AAAA;AAAA,MAC5E,eAAe,IAAI,SAAM;AA9I/B;AA8IkC,gCAAK,QAAQ,SAAQ,kBAArB,4BAAqC,GAAG;AAAA;AAAA,MACpE,UAAU,IAAI,SAAM;AA/I1B;AA+I6B,gCAAK,QAAQ,SAAQ,aAArB,4BAAgC,GAAG;AAAA;AAAA,MAC1D,gBAAgB,IAAI,SAAM;AAhJhC;AAgJmC,gCAAK,QAAQ,SAAQ,mBAArB,4BAAsC,GAAG;AAAA;AAAA,MACtE,QAAQ,IAAI,SAAM;AAjJxB;AAiJ2B,gCAAK,QAAQ,SAAQ,WAArB,4BAA8B,GAAG;AAAA;AAAA,MACtD,SAAS,IAAI,SAAM;AAlJzB;AAkJ4B,gCAAK,QAAQ,SAAQ,YAArB,4BAA+B,GAAG;AAAA;AAAA,MACxD,UAAU,IAAI,SAAM;AAnJ1B;AAmJ6B,gCAAK,QAAQ,SAAQ,aAArB,4BAAgC,GAAG;AAAA;AAAA,IAC5D;AACA,UAAM,SAAS,IAAI,mBAAO,cAAc;AAIxC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,YAA2B;AACzB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,oBAA0B;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,eAA2B;AACnC,SAAK,cAAc,IAAI,aAAa;AAEpC,WAAO,MAAM;AACX,WAAK,cAAc,OAAO,aAAa;AAAA,IACzC;AAAA,EACF;AAAA,EAEA,OAAO,eAAe,GAAqB,GAAqB;AAC9D,WAAQ,OAAO,KAAK,CAAC,EAAiC,MAAM,SAAO;AACjE,UACE;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,EAAE,SAAS,GAAG,GACd;AAEA,eAAO;AAAA,MACT;AAGA,UAAI,QAAQ,gBAAgB,EAAE,cAAc,EAAE,YAAY;AACxD,YAAI,EAAE,WAAW,WAAW,EAAE,WAAW,QAAQ;AAC/C,iBAAO;AAAA,QACT;AACA,eAAO,EAAE,WAAW,MAAM,CAAC,WAAW,UAAU;AA/MxD;AAgNU,cAAI,gBAAc,OAAE,eAAF,mBAAe,SAAQ;AACvC,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,UAAI,EAAE,GAAG,MAAM,EAAE,GAAG,GAAG;AAErB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,MAAsB;AAE7B,WAAO,MAAM;AACX,WAAK,qBAAqB;AAE1B,mBAAa,KAAK,2BAA2B;AAE7C,UAAI,KAAK,UAAU,CAAC,KAAK,OAAO,eAAe,KAAK,WAAW,GAAG;AAEhE,YAAI,CAAC,uBAAsB,eAAe,KAAK,QAAQ,SAAS,KAAK,OAAO,OAAO,GAAG;AAGpF,eAAK,OAAO,WAAW;AAAA,YACrB,GAAG,KAAK,QAAQ;AAAA,YAChB,UAAU,KAAK,OAAO;AAAA,UACxB,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AAML,aAAK,sBAAsB,IAAI;AAAA,MACjC;AAEA,aAAO,MAAM;AACX,aAAK,qBAAqB;AAC1B,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKQ,sBAAsB,MAAsB;AAClD,QAAI,KAAK,UAAU,CAAC,KAAK,OAAO,aAAa;AAE3C,UAAI,KAAK,iBAAiB,MAAM;AAE9B,aAAK,eAAe;AACpB;AAAA,MACF;AACA,YAAM,eACJ,KAAK,aAAa,WAAW,KAAK,UAAU,KAAK,aAAa,MAAM,CAAC,KAAK,UAAU,QAAQ,KAAK,KAAK,CAAC;AAEzG,UAAI,cAAc;AAEhB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,UAAU,CAAC,KAAK,OAAO,aAAa;AAE3C,WAAK,OAAO,QAAQ;AAAA,IACtB;AAEA,SAAK,UAAU,KAAK,aAAa,CAAC;AAGlC,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,kBAAkB;AACxB,UAAM,oBAAoB,KAAK;AAC/B,UAAM,gBAAgB,KAAK;AAG3B,SAAK,8BAA8B,WAAW,MAAM;AAClD,UAAI,KAAK,sBAAsB,KAAK,eAAe,mBAAmB;AAEpE,YAAI,eAAe;AAEjB,wBAAc,WAAW,KAAK,QAAQ,OAAO;AAAA,QAC/C;AACA;AAAA,MACF;AACA,UAAI,iBAAiB,CAAC,cAAc,aAAa;AAC/C,sBAAc,QAAQ;AACtB,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,UAAU,IAAI;AAAA,QACrB;AAAA,MACF;AAAA,IAGF,GAAG,CAAC;AAAA,EACN;AACF;AAuBO,SAAS,UAAU,UAA4B,CAAC,GAAG,OAAuB,CAAC,GAAkB;AAClG,QAAM,wBAAoB,sBAAO,OAAO;AAExC,oBAAkB,UAAU;AAE5B,QAAM,CAAC,eAAe,QAAI,wBAAS,MAAM,IAAI,sBAAsB,iBAAiB,CAAC;AAErF,QAAM,aAAS;AAAA,IACb,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAClB;AAEA,mCAAc,MAAM;AAIpB,+BAAU,gBAAgB,SAAS,IAAI,CAAC;AAIxC,iBAAe;AAAA,IACb;AAAA,IACA,UAAU,CAAC,EAAE,kBAAkB,MAAM;AACnC,UAAI,QAAQ,gCAAgC,SAAS,QAAQ,gCAAgC,QAAW;AAEtG,eAAO;AAAA,MACT;AAGA,UAAI,QAAQ,qBAAqB,sBAAsB,GAAG;AACxD,eAAO;AAAA,MACT;AACA,aAAO,oBAAoB;AAAA,IAC7B;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;AF3UI,IAAAC,sBAAA;AAtCG,IAAM,oBAAgB,6BAAkC;AAAA,EAC7D,QAAQ;AACV,CAAC;AAEM,IAAM,iBAAiB,cAAc;AAKrC,IAAM,mBAAmB,UAAM,0BAAW,aAAa;AAcvD,SAAS,eAAe;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB,CAAC;AAAA,EACxB,GAAG;AACL,GAAwB;AACtB,QAAM,SAAS,UAAU,aAAa;AACtC,QAAM,mBAAe,uBAAQ,OAAO,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC;AAEzD,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,SACE,8CAAC,cAAc,UAAd,EAAuB,OAAO,cAC5B;AAAA;AAAA,IACD,6CAAC,kBACE,WAAC,EAAE,QAAQ,cAAc,MAAM,6CAAC,iBAAc,QAAQ,eAAgB,GAAG,sBAAsB,GAClG;AAAA,IACC;AAAA,IACA;AAAA,KACH;AAEJ;;;AI1DA,IAAAC,gBAAyD;AAYlD,IAAM,2BAAuB,6BAAyC;AAAA,EAC3E,aAAa,MAAM;AAAA,EAEnB;AAAA,EACA,yBAAyB;AAAA,EACzB,oBAAoB,MAAM;AAAA,EAE1B;AACF,CAAC;AAEM,IAAM,+BAA+B,CAAC,EAAE,UAAU,QAAQ,MAAmD;AAClH,aAAO,6BAAc,qBAAqB,UAAU,EAAE,OAAO,EAAE,yBAAyB,QAAQ,EAAE,GAAG,QAAQ;AAC/G;AAEO,IAAM,mBAAmB,UAAM,0BAAW,oBAAoB;;;ACVjE,IAAAC;AAAA;AAAA,EAAA;AAAA;AARG,SAAS,gBAAqE;AAAA,EACnF,IAAI,MAAM;AAAA,EACV,GAAG;AACL,GAA4B;AAC1B,QAAM,EAAE,oBAAoB,wBAAwB,IAAI,iBAAiB;AAEzE,SAEE;AAAA,IAAC;AAAA;AAAA,MACE,GAAG;AAAA,MACJ,KAAK;AAAA,MACL,0BAAuB;AAAA,MACvB,OAAO;AAAA,QACL,YAAY;AAAA,QACZ,GAAG,MAAM;AAAA,MACX;AAAA,MAEC;AAAA;AAAA,EACH;AAEJ;;;AC7BA,IAAAC,gBAAkB;AAed,IAAAC;AAAA;AAAA,EAAA;AAAA;AANG,IAAM,kBAAkD,cAAAC,QAAM,WAAW,CAAC,OAAO,QAAQ;AAC9F,QAAM,EAAE,YAAY,IAAI,iBAAiB;AACzC,QAAM,MAAM,MAAM,MAAM;AAExB,SAEE;AAAA,IAAC;AAAA;AAAA,MACE,GAAG;AAAA,MACJ;AAAA,MACA,0BAAuB;AAAA,MACvB;AAAA,MACA,OAAO;AAAA,QACL,YAAY;AAAA,QACZ,GAAG,MAAM;AAAA,MACX;AAAA;AAAA,EACF;AAEJ,CAAC;;;ACxBD,IAAAC,eAAyB;AACzB,IAAAC,gBAAkB;;;ACMlB,IAAAC,gBAAwC;AACxC,IAAAC,oBAA0B;AAkNF,IAAAC,sBAAA;AAzMxB,SAAS,iBAAiB,WAAgB;AACxC,SAAO,CAAC,EAAE,OAAO,cAAc,cAAc,UAAU,aAAa,UAAU,UAAU;AAC1F;AAOA,SAAS,sBAAsB,WAAgB;AAC7C,SAAO,CAAC,EACN,OAAO,cAAc,YACrB,UAAU,aACT,UAAU,SAAS,SAAS,MAAM,+BACjC,UAAU,SAAS,gBAAgB;AAEzC;AAOA,SAAS,gBAAgB,WAAgB;AACvC,SAAO,CAAC,EACN,OAAO,cAAc,YACrB,UAAU,aACT,UAAU,SAAS,SAAS,MAAM,wBAAwB,UAAU,SAAS,gBAAgB;AAElG;AASA,SAAS,cAAc,WAAgB;AAErC,MAAI,iBAAiB,SAAS,GAAG;AAC/B,WAAO;AAAA,EACT;AAGA,MAAI,sBAAsB,SAAS,GAAG;AACpC,WAAO;AAAA,EACT;AAGA,MAAI,gBAAgB,SAAS,GAAG;AAE9B,UAAM,mBAAmB,UAAU;AACnC,QAAI,kBAAkB;AACpB,aAAO,iBAAiB,gBAAgB,KAAK,sBAAsB,gBAAgB;AAAA,IACrF;AAAA,EACF;AAEA,SAAO;AACT;AAMA,SAAS,gBAAyB;AAIhC,MAAI;AAEF,QAAI,cAAAC,SAAc;AAChB,YAAM,eAAe,SAAS,cAAAA,QAAa,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAC5D,aAAO,gBAAgB;AAAA,IACzB;AAAA,EACF,QAAQ;AAAA,EAER;AACA,SAAO;AACT;AAgDO,IAAM,gBAAN,MAAyE;AAAA;AAAA;AAAA;AAAA,EAkB9E,YACE,WACA,EAAE,QAAQ,QAAQ,CAAC,GAAG,KAAK,OAAO,YAAY,GAAG,GACjD;AARF,eAAgB;AASd,SAAK,KAAK,KAAK,MAAM,KAAK,OAAO,IAAI,UAAU,EAAE,SAAS;AAC1D,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,UAAU,SAAS,cAAc,EAAE;AACxC,SAAK,QAAQ,UAAU,IAAI,gBAAgB;AAE3C,QAAI,WAAW;AACb,WAAK,QAAQ,UAAU,IAAI,GAAG,UAAU,MAAM,GAAG,CAAC;AAAA,IACpD;AAKA,QAAI,KAAK,OAAO,eAAe;AAC7B,uCAAU,MAAM;AACd,aAAK,OAAO;AAAA,MACd,CAAC;AAAA,IACH,OAAO;AACL,qBAAe,MAAM;AACnB,aAAK,OAAO;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAe;AApMjB;AAqMI,UAAM,YAAY,KAAK;AACvB,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,KAAK;AAGpB,UAAM,YAAY,cAAc;AAChC,UAAM,yBAAyB,cAAc,SAAS;AAEtD,UAAM,eAAe,EAAE,GAAG,MAAM;AAGhC,QAAI,aAAa,OAAO,EAAE,aAAa,yBAAyB;AAC9D,aAAO,aAAa;AAAA,IACtB;AAGA,QAAI,CAAC,aAAa,QAAQ,aAAa,yBAAyB;AAE9D,mBAAa,MAAM,CAAC,QAAW;AAC7B,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAEA,SAAK,eAAe,6CAAC,aAAW,GAAG,cAAc;AAEjD,2CAAQ,qBAAR,mBAA0B,YAAY,KAAK,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,QAA6B,CAAC,GAAS;AACjD,SAAK,QAAQ;AAAA,MACX,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,IACL;AAEA,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,UAAgB;AAhPlB;AAiPI,UAAM,SAAS,KAAK;AAEpB,2CAAQ,qBAAR,mBAA0B,eAAe,KAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,YAA0C;AACzD,WAAO,KAAK,UAAU,EAAE,QAAQ,SAAO;AACrC,WAAK,QAAQ,aAAa,KAAK,WAAW,GAAG,CAAC;AAAA,IAChD,CAAC;AAAA,EACH;AACF;;;ADjOI,IAAAC;AAAA;AAAA,EAAA;AAAA;AAlBG,IAAM,uBAAuB,cAAAC,QAAM,cAAoC;AAAA,EAC5E,oBAAoB,MAAM;AAAA,EAE1B;AACF,CAAC;AAMM,IAAM,kBAAkB,CAC7B,UACG;AACH,QAAM,EAAE,IAAI,MAAM,QAAQ,GAAG,KAAK,IAAI;AACtC,QAAM,EAAE,mBAAmB,IAAI,cAAAA,QAAM,WAAW,oBAAoB;AAEpE,SAEE,6CAAC,OAAK,GAAG,MAAM,KAAK,oBAAoB,0BAAuB,IAAG;AAEtE;AAWO,IAAM,gBAAN,cAA4B,sBAA2E;AAAA,EAK5G,YACE,WACA,OACA,SACA;AACA,UAAM,WAAW,OAAO,OAAO;AAPjC,qCAA4B;AAS1B,UAAM,EAAE,KAAK,QAAQ,OAAO,YAAY,GAAG,IAAI,WAAW,CAAC;AAC3D,UAAM,iBAAiB,EAAE,GAAG,OAAO,kBAAkB,KAAK,iBAAiB,KAAK,IAAI,EAAE;AAEtF,SAAK,oBAAoB,SAAS,cAAc,MAAM;AAEtD,UAAM,qBAAiE,QAAM;AAC3E,UAAI,MAAM,KAAK,qBAAqB,GAAG,eAAe,KAAK,mBAAmB;AAC5E,WAAG,YAAY,KAAK,iBAAiB;AACrC,aAAK,4BAA4B;AAAA,MACnC;AAAA,IACF;AACA,UAAM,UAAgC;AAAA,MACpC;AAAA,IACF;AAIA,UAAM,wBAAgE,cAAAA,QAAM,KAAK,CAAAC,oBAAkB;AACjG,aACE,6CAAC,qBAAqB,UAArB,EAA8B,OAAO,SACnC,wBAAAD,QAAM,cAAc,WAAWC,eAAc,GAChD;AAAA,IAEJ,CAAC;AAED,0BAAsB,cAAc;AAEpC,SAAK,WAAW,IAAI,cAAc,uBAAuB;AAAA,MACvD,QAAQ,MAAM;AAAA,MACd,OAAO;AAAA,MACP;AAAA,MACA,WAAW,QAAQ,MAAM,KAAK,KAAK,IAAI,IAAI,SAAS,GAAG,KAAK;AAAA,IAC9D,CAAC;AAED,QAAI,OAAO;AACT,WAAK,SAAS,iBAAiB,KAAK;AAAA,IACtC;AAAA,EACF;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,IAAI,aAAa;AACf,QAAI,CAAC,KAAK,2BAA2B;AACnC,aAAO;AAAA,IACT;AACA,WAAO,KAAK;AAAA,EACd;AACF;AAEO,SAAS,sBACd,WACA,UAAiD,CAAC,GAChC;AAClB,SAAO,WAAS,IAAI,cAAc,WAAW,OAAO,OAAO;AAC7D;;;AEvGA,IAAAC,eAAgD;AAIhD,IAAAC,iBAA+C;AAkIvC,IAAAC,sBAAA;AAxFD,IAAM,gBAAN,cAKG,sBAAyC;AAAA,EAWjD,YAAY,WAAsB,OAA8B,SAA4B;AAC1F,UAAM,WAAW,OAAO,OAAO;AAE/B,QAAI,CAAC,KAAK,KAAK,QAAQ;AACrB,UAAI,KAAK,QAAQ,sBAAsB;AACrC,aAAK,oBAAoB,SAAS,cAAc,KAAK,QAAQ,oBAAoB;AAAA,MACnF,OAAO;AACL,aAAK,oBAAoB,SAAS,cAAc,KAAK,KAAK,WAAW,SAAS,KAAK;AAAA,MACrF;AAEA,WAAK,kBAAkB,QAAQ,uBAAuB;AACtD,WAAK,kBAAkB,QAAQ,kBAAkB;AAKjD,WAAK,kBAAkB,MAAM,aAAa;AAE1C,YAAM,gBAAgB,KAAK,IAAI,cAAc,0BAA0B;AAEvE,UAAI,CAAC,eAAe;AAClB;AAAA,MACF;AAEA,oBAAc,YAAY,KAAK,iBAAiB;AAAA,IAClD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,UAAM,QAAQ;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,MACX,aAAa,KAAK;AAAA,MAClB,kBAAkB,KAAK;AAAA,MACvB,MAAM,KAAK;AAAA,MACX,UAAU;AAAA,MACV,WAAW,KAAK;AAAA,MAChB,gBAAgB,KAAK;AAAA,MACrB,QAAQ,MAAM,KAAK,OAAO;AAAA,MAC1B,kBAAkB,CAAC,aAAa,CAAC,MAAM,KAAK,iBAAiB,UAAU;AAAA,MACvE,YAAY,MAAM,KAAK,WAAW;AAAA,MAClC,SAAK,0BAAa;AAAA,IACpB;AAEA,QAAI,CAAE,KAAK,UAAkB,aAAa;AACxC,YAAM,sBAAsB,CAAC,WAA2B;AACtD,eAAO,OAAO,OAAO,CAAC,EAAE,YAAY,IAAI,OAAO,UAAU,CAAC;AAAA,MAC5D;AAEA,WAAK,UAAU,cAAc,oBAAoB,KAAK,UAAU,IAAI;AAAA,IACtE;AAEA,UAAM,cAAc,KAAK,YAAY,KAAK,IAAI;AAC9C,UAAM,qBAAsE,aAAW;AACrF,UAAI,WAAW,KAAK,qBAAqB,QAAQ,eAAe,KAAK,mBAAmB;AAEtF,YAAI,QAAQ,aAAa,wBAAwB,GAAG;AAClD,kBAAQ,gBAAgB,wBAAwB;AAAA,QAClD;AACA,gBAAQ,YAAY,KAAK,iBAAiB;AAAA,MAC5C;AAAA,IACF;AACA,UAAM,UAAU,EAAE,aAAa,mBAAmB;AAClD,UAAM,YAAY,KAAK;AAGvB,UAAM,4BAAqE,qBAAK,oBAAkB;AAChG,aACE,6CAAC,qBAAqB,UAArB,EAA8B,OAAO,SACnC,4CAAc,WAAW,cAAc,GAC1C;AAAA,IAEJ,CAAC;AAED,0BAAsB,cAAc;AAEpC,QAAI,KAAK,KAAK,KAAK,WAAW,SAAS;AAEvC,QAAI,KAAK,QAAQ,IAAI;AACnB,WAAK,KAAK,QAAQ;AAAA,IACpB;AAEA,UAAM,EAAE,YAAY,GAAG,IAAI,KAAK;AAEhC,SAAK,wBAAwB,KAAK,sBAAsB,KAAK,IAAI;AAEjE,SAAK,WAAW,IAAI,cAAc,uBAAuB;AAAA,MACvD,QAAQ,KAAK;AAAA,MACb;AAAA,MACA;AAAA,MACA,WAAW,QAAQ,KAAK,KAAK,KAAK,IAAI,IAAI,SAAS,GAAG,KAAK;AAAA,IAC7D,CAAC;AAED,SAAK,OAAO,GAAG,mBAAmB,KAAK,qBAAqB;AAC5D,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,MAAM;AA9KZ;AA+KI,QACE,KAAK,SAAS,QAAQ,qBACtB,GAAC,UAAK,SAAS,QAAQ,sBAAtB,mBAAyC,aAAa,4BACvD;AACA,YAAM,MAAM,8DAA8D;AAAA,IAC5E;AAEA,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aAAa;AACf,QAAI,KAAK,KAAK,QAAQ;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB;AACtB,UAAM,EAAE,MAAM,GAAG,IAAI,KAAK,OAAO,MAAM;AACvC,UAAM,MAAM,KAAK,OAAO;AAExB,QAAI,OAAO,QAAQ,UAAU;AAC3B;AAAA,IACF;AAEA,QAAI,QAAQ,OAAO,MAAM,MAAM,KAAK,KAAK,UAAU;AACjD,UAAI,KAAK,SAAS,MAAM,UAAU;AAChC;AAAA,MACF;AAEA,WAAK,WAAW;AAAA,IAClB,OAAO;AACL,UAAI,CAAC,KAAK,SAAS,MAAM,UAAU;AACjC;AAAA,MACF;AAEA,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,MAAY,aAAoC,kBAA6C;AAClG,UAAM,oBAAoB,CAAC,UAAgC;AACzD,WAAK,SAAS,YAAY,KAAK;AAC/B,UAAI,OAAO,KAAK,QAAQ,UAAU,YAAY;AAC5C,aAAK,wBAAwB;AAAA,MAC/B;AAAA,IACF;AAEA,QAAI,KAAK,SAAS,KAAK,KAAK,MAAM;AAChC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,KAAK,QAAQ,WAAW,YAAY;AAC7C,YAAM,UAAU,KAAK;AACrB,YAAM,iBAAiB,KAAK;AAC5B,YAAM,sBAAsB,KAAK;AAEjC,WAAK,OAAO;AACZ,WAAK,cAAc;AACnB,WAAK,mBAAmB;AAExB,aAAO,KAAK,QAAQ,OAAO;AAAA,QACzB;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA,aAAa,MAAM,kBAAkB,EAAE,MAAM,aAAa,iBAAiB,CAAC;AAAA,MAC9E,CAAC;AAAA,IACH;AAEA,QAAI,SAAS,KAAK,QAAQ,KAAK,gBAAgB,eAAe,KAAK,qBAAqB,kBAAkB;AACxG,aAAO;AAAA,IACT;AAEA,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,mBAAmB;AAExB,sBAAkB,EAAE,MAAM,aAAa,iBAAiB,CAAC;AAEzD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACX,SAAK,SAAS,YAAY;AAAA,MACxB,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,SAAS,QAAQ,UAAU,IAAI,0BAA0B;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACb,SAAK,SAAS,YAAY;AAAA,MACxB,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,SAAS,QAAQ,UAAU,OAAO,0BAA0B;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,SAAS,QAAQ;AACtB,SAAK,OAAO,IAAI,mBAAmB,KAAK,qBAAqB;AAC7D,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,0BAA0B;AACxB,QAAI,KAAK,QAAQ,OAAO;AACtB,UAAI,WAAmC,CAAC;AAExC,UAAI,OAAO,KAAK,QAAQ,UAAU,YAAY;AAC5C,cAAM,sBAAsB,KAAK,OAAO,iBAAiB;AACzD,cAAM,qBAAiB,oCAAsB,KAAK,MAAM,mBAAmB;AAE3E,mBAAW,KAAK,QAAQ,MAAM,EAAE,MAAM,KAAK,MAAM,eAAe,CAAC;AAAA,MACnE,OAAO;AACL,mBAAW,KAAK,QAAQ;AAAA,MAC1B;AAEA,WAAK,SAAS,iBAAiB,QAAQ;AAAA,IACzC;AAAA,EACF;AACF;AAKO,SAAS,sBACd,WACA,SACkB;AAClB,SAAO,WAAS;AAId,QAAI,CAAE,MAAM,OAAsC,kBAAkB;AAClE,aAAO,CAAC;AAAA,IACV;AAEA,WAAO,IAAI,cAAiB,WAAW,OAAO,OAAO;AAAA,EACvD;AACF;;;AV5UA,0BAAc,yBAXd;", "names": ["import_react", "ReactDOM", "React", "import_react", "import_shim", "import_react", "deepEqual", "import_jsx_runtime", "import_react", "import_jsx_runtime", "import_react", "import_jsx_runtime", "React", "import_core", "import_react", "import_react", "import_react_dom", "import_jsx_runtime", "reactVersion", "import_jsx_runtime", "React", "componentProps", "import_core", "import_react", "import_jsx_runtime"]}