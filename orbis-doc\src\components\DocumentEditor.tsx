'use client'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { useEffect, useState } from 'react'
import { PaginationPlus } from 'tiptap-pagination-plus'

interface DocumentEditorProps {
  content?: string
  onChange?: (content: string) => void
}

export default function DocumentEditor({ content = '', onChange }: DocumentEditorProps) {
  const [isMounted, setIsMounted] = useState(false)

  const editor = useEditor({
    extensions: [
      StarterKit,
      PaginationPlus.configure({
        pageSize: {
          width: 210, // A4 width in mm
          height: 297, // A4 height in mm
        },
        pageMargins: {
          top: 25,
          bottom: 25,
          left: 25,
          right: 25,
        },
        headerHeight: 20,
        footerHeight: 20,
        showPageNumbers: true,
        pageNumberPosition: 'bottom-center',
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      onChange?.(html)
    },
    editorProps: {
      attributes: {
        class: 'tiptap-editor focus:outline-none',
      },
    },
  })

  // Gérer l'hydratation côté client
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Éviter les erreurs d'hydratation
  if (!isMounted) {
    return (
      <div className="min-h-screen bg-gray-100 p-8">
        <div className="max-w-4xl mx-auto bg-white shadow-lg">
          <div className="h-96 flex items-center justify-center text-gray-500">
            Chargement de l'éditeur...
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-5xl mx-auto">
        {/* Conteneur de l'éditeur avec pagination */}
        <div className="bg-white shadow-lg">
          <EditorContent editor={editor} />
        </div>
      </div>
    </div>
  )
}
