'use client'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { useEffect, useState } from 'react'
import {
  PaginationPlus,
  TablePlus,
  TableRowPlus,
  TableCellPlus,
  TableHeaderPlus
} from 'tiptap-pagination-plus'
import '../styles/editor.css'

interface DocumentEditorProps {
  content?: string
  onChange?: (content: string) => void
}

export default function DocumentEditor({ content = '', onChange }: DocumentEditorProps) {
  const [isMounted, setIsMounted] = useState(false)

  // Gérer l'hydratation côté client
  useEffect(() => {
    setIsMounted(true)
  }, [])

  const editor = useEditor({
    extensions: [
      StarterKit,
      // Temporairement sans PaginationPlus pour tester
    ],
    content,
    immediatelyRender: false, // Éviter les erreurs d'hydratation SSR
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      onChange?.(html)
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none p-8',
      },
    },
  })

  // Éviter les erreurs d'hydratation
  if (!isMounted) {
    return (
      <div className="min-h-screen bg-gray-100 p-8">
        <div className="max-w-4xl mx-auto bg-white shadow-lg">
          <div className="h-96 flex items-center justify-center text-gray-500">
            Chargement de l'éditeur...
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <EditorContent editor={editor} />
    </div>
  )
}
