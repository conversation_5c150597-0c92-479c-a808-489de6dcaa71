## 1.8.2 (2025-04-22)

### Bug fixes

Make sure the drop cursor is positioned in the right place when the editor is scaled with a CSS transform.

Fix the width of the cursor when in a transformed element. Fix incorrect check in dragleave handler

<PERSON>x an issue where the dropcursor would hide when drag moved over the top editor element.

## 1.8.1 (2023-05-17)

### Bug fixes

Include CommonJS type declarations in the package to please new TypeScript resolution settings.

## 1.8.0 (2023-03-27)

### New features

If the `color` option is set to `false`, the library will not assign an explicit color to the cursor.

## 1.7.1 (2023-03-02)

### Bug fixes

Don't hide the drop cursor when no valid drop point can be found below the pointer.

## 1.7.0 (2023-02-07)

### New features

The drop cursor element now has a `prosemirror-blockcursor-block`/`inline` CSS class depending on whether it is in a block or inline position.

## 1.6.1 (2022-10-25)

### Bug fixes

Fix a crash when there's no DOM for the node next to the drag position.

## 1.6.0 (2022-08-07)

### New features

`disableDropCursor` is now passed the dragover event as 3rd argument.

## 1.5.0 (2022-05-30)

### New features

Include TypeScript type declarations.

## 1.4.0 (2021-11-11)

### New features

Add support for a `disableDropCursor` property on node specs, which can be used to turn off the drop cursor inside such nodes.

## 1.3.5 (2021-05-25)

### Bug fixes

Avoid a crash that happened when the document shrank during dragging.

## 1.3.4 (2021-04-01)

### Bug fixes

Hide the drop cursor when `dropPoint` doesn't return a position.

## 1.3.3 (2021-02-04)

### Bug fixes

Fix drop cursor positioning when the editor's `offsetParent` has been scrolled.

## 1.3.2 (2019-11-20)

### Bug fixes

Rename ES module files to use a .js extension, since Webpack gets confused by .mjs

## 1.3.1 (2019-11-19)

### Bug fixes

The file referred to in the package's `module` field now is compiled down to ES5.

## 1.3.0 (2019-11-08)

### New features

Add a `module` field to package json file.

## 1.2.0 (2019-10-08)

### New features

`dropCursor` now takes a new option, `class`, to set the CSS class name of the cursor element. Add class option to in-code docs

## 1.1.2 (2019-09-05)

### Bug fixes

Fix crash on IE11 due to using a method that platform doesn't support. Don't show a drop cursor when the view isn't editable

The drop cursor will no longer show up when the view isn't editable.

## 1.1.1 (2018-10-23)

### Bug fixes

Fix crash when destroying the plugin, due to a misspelled method name.

## 1.1.0 (2018-10-22)

### Bug fixes

Fixes an issue where drop cursors changed line breaking, causing the content to jump around during dragging.

### New features

Between-blocks drop cursors are now shown as a horizontal line.

## 1.0.1 (2018-06-20)

### Bug fixes

Dragging from a content node directly to the outside of the editor will now properly hide the drop cursor.

Make removal of drop cursor part of the drop transaction when possible.

Use `dropPoint` from prosemirror-transform, rather than a local parallel implementation.

## 1.0.0 (2017-10-13)

First stable release.
