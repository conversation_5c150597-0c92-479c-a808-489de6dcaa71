{"name": "@tiptap/react", "description": "React components for tiptap", "version": "3.0.7", "homepage": "https://tiptap.dev", "keywords": ["tiptap", "tiptap react components"], "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/ueberdosis"}, "exports": {".": {"types": {"import": "./dist/index.d.ts", "require": "./dist/index.d.cts"}, "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./menus": {"types": {"import": "./dist/menus/index.d.ts", "require": "./dist/menus/index.d.cts"}, "import": "./dist/menus/index.js", "require": "./dist/menus/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "files": ["src", "dist"], "dependencies": {"@types/use-sync-external-store": "^0.0.6", "fast-deep-equal": "^3.1.3", "use-sync-external-store": "^1.4.0"}, "devDependencies": {"@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "@tiptap/core": "^3.0.7", "@tiptap/pm": "^3.0.7"}, "optionalDependencies": {"@tiptap/extension-floating-menu": "^3.0.7", "@tiptap/extension-bubble-menu": "^3.0.7"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0", "@tiptap/core": "^3.0.7", "@tiptap/pm": "^3.0.7"}, "repository": {"type": "git", "url": "https://github.com/ueberdosis/tiptap", "directory": "packages/react"}, "sideEffects": false, "scripts": {"build": "tsup", "lint": "prettier ./src/ --check && eslint --cache --quiet --no-error-on-unmatched-pattern ./src/"}}