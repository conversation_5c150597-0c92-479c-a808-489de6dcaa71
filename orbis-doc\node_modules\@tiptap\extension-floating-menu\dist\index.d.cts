import { Editor, Extension } from '@tiptap/core';
import * as _floating_ui_dom from '@floating-ui/dom';
import { offset, flip, shift, arrow, size, autoPlacement, hide, inline } from '@floating-ui/dom';
import { PluginKey, EditorState, Plugin } from '@tiptap/pm/state';
import { EditorView } from '@tiptap/pm/view';

interface FloatingMenuPluginProps {
    /**
     * The plugin key for the floating menu.
     * @default 'floatingMenu'
     */
    pluginKey: PluginKey | string;
    /**
     * The editor instance.
     * @default null
     */
    editor: Editor;
    /**
     * The DOM element that contains your menu.
     * @default null
     */
    element: HTMLElement;
    /**
     * A function that determines whether the menu should be shown or not.
     * If this function returns `false`, the menu will be hidden, otherwise it will be shown.
     */
    shouldShow?: ((props: {
        editor: Editor;
        view: EditorView;
        state: EditorState;
        oldState?: EditorState;
        from: number;
        to: number;
    }) => boolean) | null;
    /**
     * The options for the floating menu. Those are passed to Floating UI and include options for the placement, offset, flip, shift, arrow, size, autoPlacement,
     * hide, and inline middlewares.
     * @default {}
     * @see https://floating-ui.com/docs/computePosition#options
     */
    options?: {
        strategy?: 'absolute' | 'fixed';
        placement?: 'top' | 'right' | 'bottom' | 'left' | 'top-start' | 'top-end' | 'right-start' | 'right-end' | 'bottom-start' | 'bottom-end' | 'left-start' | 'left-end';
        offset?: Parameters<typeof offset>[0] | boolean;
        flip?: Parameters<typeof flip>[0] | boolean;
        shift?: Parameters<typeof shift>[0] | boolean;
        arrow?: Parameters<typeof arrow>[0] | false;
        size?: Parameters<typeof size>[0] | boolean;
        autoPlacement?: Parameters<typeof autoPlacement>[0] | boolean;
        hide?: Parameters<typeof hide>[0] | boolean;
        inline?: Parameters<typeof inline>[0] | boolean;
        onShow?: () => void;
        onHide?: () => void;
        onUpdate?: () => void;
        onDestroy?: () => void;
    };
}
type FloatingMenuViewProps = FloatingMenuPluginProps & {
    /**
     * The editor view.
     */
    view: EditorView;
};
declare class FloatingMenuView {
    editor: Editor;
    element: HTMLElement;
    view: EditorView;
    preventHide: boolean;
    private isVisible;
    private getTextContent;
    shouldShow: Exclude<FloatingMenuPluginProps['shouldShow'], null>;
    private floatingUIOptions;
    get middlewares(): {
        options?: any;
        name: string;
        fn: (state: _floating_ui_dom.MiddlewareState) => _floating_ui_dom.MiddlewareReturn | Promise<_floating_ui_dom.MiddlewareReturn>;
    }[];
    constructor({ editor, element, view, options, shouldShow }: FloatingMenuViewProps);
    getShouldShow(oldState?: EditorState): boolean | undefined;
    updateHandler: (view: EditorView, selectionChanged: boolean, docChanged: boolean, oldState?: EditorState) => void;
    mousedownHandler: () => void;
    focusHandler: () => void;
    blurHandler: ({ event }: {
        event: FocusEvent;
    }) => void;
    updatePosition(): void;
    update(view: EditorView, oldState?: EditorState): void;
    show(): void;
    hide(): void;
    destroy(): void;
}
declare const FloatingMenuPlugin: (options: FloatingMenuPluginProps) => Plugin<any>;

type FloatingMenuOptions = Omit<FloatingMenuPluginProps, 'editor' | 'element'> & {
    /**
     * The DOM element that contains your menu.
     * @type {HTMLElement}
     * @default null
     */
    element: HTMLElement | null;
};
/**
 * This extension allows you to create a floating menu.
 * @see https://tiptap.dev/api/extensions/floating-menu
 */
declare const FloatingMenu: Extension<FloatingMenuOptions, any>;

export { FloatingMenu, type FloatingMenuOptions, FloatingMenuPlugin, type FloatingMenuPluginProps, FloatingMenuView, type FloatingMenuViewProps, FloatingMenu as default };
