{"version": 3, "sources": ["../src/index.ts", "../src/floating-menu.ts", "../src/floating-menu-plugin.ts"], "sourcesContent": ["import { FloatingMenu } from './floating-menu.js'\n\nexport * from './floating-menu.js'\nexport * from './floating-menu-plugin.js'\n\nexport default FloatingMenu\n", "import { Extension } from '@tiptap/core'\n\nimport type { FloatingMenuPluginProps } from './floating-menu-plugin.js'\nimport { FloatingMenuPlugin } from './floating-menu-plugin.js'\n\nexport type FloatingMenuOptions = Omit<FloatingMenuPluginProps, 'editor' | 'element'> & {\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement | null\n}\n\n/**\n * This extension allows you to create a floating menu.\n * @see https://tiptap.dev/api/extensions/floating-menu\n */\nexport const FloatingMenu = Extension.create<FloatingMenuOptions>({\n  name: 'floatingMenu',\n\n  addOptions() {\n    return {\n      element: null,\n      options: {},\n      pluginKey: 'floatingMenu',\n      shouldShow: null,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    if (!this.options.element) {\n      return []\n    }\n\n    return [\n      FloatingMenuPlugin({\n        pluginKey: this.options.plugin<PERSON><PERSON>,\n        editor: this.editor,\n        element: this.options.element,\n        options: this.options.options,\n        shouldShow: this.options.shouldShow,\n      }),\n    ]\n  },\n})\n", "import {\n  type Middleware,\n  arrow,\n  autoPlacement,\n  computePosition,\n  flip,\n  hide,\n  inline,\n  offset,\n  shift,\n  size,\n} from '@floating-ui/dom'\nimport type { Editor } from '@tiptap/core'\nimport { getText, getTextSerializersFromSchema, posToDOMRect } from '@tiptap/core'\nimport type { Node as ProsemirrorNode } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\nimport type { EditorView } from '@tiptap/pm/view'\n\nexport interface FloatingMenuPluginProps {\n  /**\n   * The plugin key for the floating menu.\n   * @default 'floatingMenu'\n   */\n  pluginKey: PluginKey | string\n\n  /**\n   * The editor instance.\n   * @default null\n   */\n  editor: Editor\n\n  /**\n   * The DOM element that contains your menu.\n   * @default null\n   */\n  element: HTMLElement\n\n  /**\n   * A function that determines whether the menu should be shown or not.\n   * If this function returns `false`, the menu will be hidden, otherwise it will be shown.\n   */\n  shouldShow?:\n    | ((props: {\n        editor: Editor\n        view: EditorView\n        state: EditorState\n        oldState?: EditorState\n        from: number\n        to: number\n      }) => boolean)\n    | null\n\n  /**\n   * The options for the floating menu. Those are passed to Floating UI and include options for the placement, offset, flip, shift, arrow, size, autoPlacement,\n   * hide, and inline middlewares.\n   * @default {}\n   * @see https://floating-ui.com/docs/computePosition#options\n   */\n  options?: {\n    strategy?: 'absolute' | 'fixed'\n    placement?:\n      | 'top'\n      | 'right'\n      | 'bottom'\n      | 'left'\n      | 'top-start'\n      | 'top-end'\n      | 'right-start'\n      | 'right-end'\n      | 'bottom-start'\n      | 'bottom-end'\n      | 'left-start'\n      | 'left-end'\n    offset?: Parameters<typeof offset>[0] | boolean\n    flip?: Parameters<typeof flip>[0] | boolean\n    shift?: Parameters<typeof shift>[0] | boolean\n    arrow?: Parameters<typeof arrow>[0] | false\n    size?: Parameters<typeof size>[0] | boolean\n    autoPlacement?: Parameters<typeof autoPlacement>[0] | boolean\n    hide?: Parameters<typeof hide>[0] | boolean\n    inline?: Parameters<typeof inline>[0] | boolean\n\n    onShow?: () => void\n    onHide?: () => void\n    onUpdate?: () => void\n    onDestroy?: () => void\n  }\n}\n\nexport type FloatingMenuViewProps = FloatingMenuPluginProps & {\n  /**\n   * The editor view.\n   */\n  view: EditorView\n}\n\nexport class FloatingMenuView {\n  public editor: Editor\n\n  public element: HTMLElement\n\n  public view: EditorView\n\n  public preventHide = false\n\n  private isVisible = false\n\n  private getTextContent(node: ProsemirrorNode) {\n    return getText(node, { textSerializers: getTextSerializersFromSchema(this.editor.schema) })\n  }\n\n  public shouldShow: Exclude<FloatingMenuPluginProps['shouldShow'], null> = ({ view, state }) => {\n    const { selection } = state\n    const { $anchor, empty } = selection\n    const isRootDepth = $anchor.depth === 1\n\n    const isEmptyTextBlock =\n      $anchor.parent.isTextblock &&\n      !$anchor.parent.type.spec.code &&\n      !$anchor.parent.textContent &&\n      $anchor.parent.childCount === 0 &&\n      !this.getTextContent($anchor.parent)\n\n    if (!view.hasFocus() || !empty || !isRootDepth || !isEmptyTextBlock || !this.editor.isEditable) {\n      return false\n    }\n\n    return true\n  }\n\n  private floatingUIOptions: NonNullable<FloatingMenuPluginProps['options']> = {\n    strategy: 'absolute',\n    placement: 'right',\n    offset: 8,\n    flip: {},\n    shift: {},\n    arrow: false,\n    size: false,\n    autoPlacement: false,\n    hide: false,\n    inline: false,\n  }\n\n  get middlewares() {\n    const middlewares: Middleware[] = []\n\n    if (this.floatingUIOptions.flip) {\n      middlewares.push(flip(typeof this.floatingUIOptions.flip !== 'boolean' ? this.floatingUIOptions.flip : undefined))\n    }\n\n    if (this.floatingUIOptions.shift) {\n      middlewares.push(\n        shift(typeof this.floatingUIOptions.shift !== 'boolean' ? this.floatingUIOptions.shift : undefined),\n      )\n    }\n\n    if (this.floatingUIOptions.offset) {\n      middlewares.push(\n        offset(typeof this.floatingUIOptions.offset !== 'boolean' ? this.floatingUIOptions.offset : undefined),\n      )\n    }\n\n    if (this.floatingUIOptions.arrow) {\n      middlewares.push(arrow(this.floatingUIOptions.arrow))\n    }\n\n    if (this.floatingUIOptions.size) {\n      middlewares.push(size(typeof this.floatingUIOptions.size !== 'boolean' ? this.floatingUIOptions.size : undefined))\n    }\n\n    if (this.floatingUIOptions.autoPlacement) {\n      middlewares.push(\n        autoPlacement(\n          typeof this.floatingUIOptions.autoPlacement !== 'boolean' ? this.floatingUIOptions.autoPlacement : undefined,\n        ),\n      )\n    }\n\n    if (this.floatingUIOptions.hide) {\n      middlewares.push(hide(typeof this.floatingUIOptions.hide !== 'boolean' ? this.floatingUIOptions.hide : undefined))\n    }\n\n    if (this.floatingUIOptions.inline) {\n      middlewares.push(\n        inline(typeof this.floatingUIOptions.inline !== 'boolean' ? this.floatingUIOptions.inline : undefined),\n      )\n    }\n\n    return middlewares\n  }\n\n  constructor({ editor, element, view, options, shouldShow }: FloatingMenuViewProps) {\n    this.editor = editor\n    this.element = element\n    this.view = view\n\n    this.floatingUIOptions = {\n      ...this.floatingUIOptions,\n      ...options,\n    }\n\n    this.element.tabIndex = 0\n\n    if (shouldShow) {\n      this.shouldShow = shouldShow\n    }\n\n    this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.editor.on('focus', this.focusHandler)\n    this.editor.on('blur', this.blurHandler)\n\n    this.update(view, view.state)\n\n    if (this.getShouldShow()) {\n      this.show()\n    }\n  }\n\n  getShouldShow(oldState?: EditorState) {\n    const { state } = this.view\n    const { selection } = state\n\n    const { ranges } = selection\n    const from = Math.min(...ranges.map(range => range.$from.pos))\n    const to = Math.max(...ranges.map(range => range.$to.pos))\n\n    const shouldShow = this.shouldShow?.({\n      editor: this.editor,\n      view: this.view,\n      state,\n      oldState,\n      from,\n      to,\n    })\n\n    return shouldShow\n  }\n\n  updateHandler = (view: EditorView, selectionChanged: boolean, docChanged: boolean, oldState?: EditorState) => {\n    const { composing } = view\n\n    const isSame = !selectionChanged && !docChanged\n\n    if (composing || isSame) {\n      return\n    }\n\n    const shouldShow = this.getShouldShow(oldState)\n\n    if (!shouldShow) {\n      this.hide()\n\n      return\n    }\n\n    this.updatePosition()\n    this.show()\n  }\n\n  mousedownHandler = () => {\n    this.preventHide = true\n  }\n\n  focusHandler = () => {\n    // we use `setTimeout` to make sure `selection` is already updated\n    setTimeout(() => this.update(this.editor.view))\n  }\n\n  blurHandler = ({ event }: { event: FocusEvent }) => {\n    if (this.preventHide) {\n      this.preventHide = false\n\n      return\n    }\n\n    if (event?.relatedTarget && this.element.parentNode?.contains(event.relatedTarget as Node)) {\n      return\n    }\n\n    if (event?.relatedTarget === this.editor.view.dom) {\n      return\n    }\n\n    this.hide()\n  }\n\n  updatePosition() {\n    const { selection } = this.editor.state\n\n    const domRect = posToDOMRect(this.view, selection.from, selection.to)\n\n    const virtualElement = {\n      getBoundingClientRect: () => domRect,\n      getClientRects: () => [domRect],\n    }\n\n    computePosition(virtualElement, this.element, {\n      placement: this.floatingUIOptions.placement,\n      strategy: this.floatingUIOptions.strategy,\n      middleware: this.middlewares,\n    }).then(({ x, y, strategy }) => {\n      this.element.style.width = 'max-content'\n      this.element.style.position = strategy\n      this.element.style.left = `${x}px`\n      this.element.style.top = `${y}px`\n\n      if (this.isVisible && this.floatingUIOptions.onUpdate) {\n        this.floatingUIOptions.onUpdate()\n      }\n    })\n  }\n\n  update(view: EditorView, oldState?: EditorState) {\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    this.updateHandler(view, selectionChanged, docChanged, oldState)\n  }\n\n  show() {\n    if (this.isVisible) {\n      return\n    }\n\n    this.element.style.visibility = 'visible'\n    this.element.style.opacity = '1'\n    // attach to editor's parent element\n    this.view.dom.parentElement?.appendChild(this.element)\n\n    if (this.floatingUIOptions.onShow) {\n      this.floatingUIOptions.onShow()\n    }\n\n    this.isVisible = true\n  }\n\n  hide() {\n    if (!this.isVisible) {\n      return\n    }\n\n    this.element.style.visibility = 'hidden'\n    this.element.style.opacity = '0'\n    // remove from the parent element\n    this.element.remove()\n\n    if (this.floatingUIOptions.onHide) {\n      this.floatingUIOptions.onHide()\n    }\n\n    this.isVisible = false\n  }\n\n  destroy() {\n    this.hide()\n    this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.editor.off('focus', this.focusHandler)\n    this.editor.off('blur', this.blurHandler)\n\n    if (this.floatingUIOptions.onDestroy) {\n      this.floatingUIOptions.onDestroy()\n    }\n  }\n}\n\nexport const FloatingMenuPlugin = (options: FloatingMenuPluginProps) => {\n  return new Plugin({\n    key: typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,\n    view: view => new FloatingMenuView({ view, ...options }),\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAA,eAA0B;;;ACA1B,iBAWO;AAEP,kBAAoE;AAGpE,mBAAkC;AAiF3B,IAAM,mBAAN,MAAuB;AAAA,EA+F5B,YAAY,EAAE,QAAQ,SAAS,MAAM,SAAS,WAAW,GAA0B;AAxFnF,SAAO,cAAc;AAErB,SAAQ,YAAY;AAMpB,SAAO,aAAmE,CAAC,EAAE,MAAM,MAAM,MAAM;AAC7F,YAAM,EAAE,UAAU,IAAI;AACtB,YAAM,EAAE,SAAS,MAAM,IAAI;AAC3B,YAAM,cAAc,QAAQ,UAAU;AAEtC,YAAM,mBACJ,QAAQ,OAAO,eACf,CAAC,QAAQ,OAAO,KAAK,KAAK,QAC1B,CAAC,QAAQ,OAAO,eAChB,QAAQ,OAAO,eAAe,KAC9B,CAAC,KAAK,eAAe,QAAQ,MAAM;AAErC,UAAI,CAAC,KAAK,SAAS,KAAK,CAAC,SAAS,CAAC,eAAe,CAAC,oBAAoB,CAAC,KAAK,OAAO,YAAY;AAC9F,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,SAAQ,oBAAqE;AAAA,MAC3E,UAAU;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,MAAM,CAAC;AAAA,MACP,OAAO,CAAC;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAiGA,yBAAgB,CAAC,MAAkB,kBAA2B,YAAqB,aAA2B;AAC5G,YAAM,EAAE,UAAU,IAAI;AAEtB,YAAM,SAAS,CAAC,oBAAoB,CAAC;AAErC,UAAI,aAAa,QAAQ;AACvB;AAAA,MACF;AAEA,YAAM,aAAa,KAAK,cAAc,QAAQ;AAE9C,UAAI,CAAC,YAAY;AACf,aAAK,KAAK;AAEV;AAAA,MACF;AAEA,WAAK,eAAe;AACpB,WAAK,KAAK;AAAA,IACZ;AAEA,4BAAmB,MAAM;AACvB,WAAK,cAAc;AAAA,IACrB;AAEA,wBAAe,MAAM;AAEnB,iBAAW,MAAM,KAAK,OAAO,KAAK,OAAO,IAAI,CAAC;AAAA,IAChD;AAEA,uBAAc,CAAC,EAAE,MAAM,MAA6B;AA7QtD;AA8QI,UAAI,KAAK,aAAa;AACpB,aAAK,cAAc;AAEnB;AAAA,MACF;AAEA,WAAI,+BAAO,oBAAiB,UAAK,QAAQ,eAAb,mBAAyB,SAAS,MAAM,iBAAwB;AAC1F;AAAA,MACF;AAEA,WAAI,+BAAO,mBAAkB,KAAK,OAAO,KAAK,KAAK;AACjD;AAAA,MACF;AAEA,WAAK,KAAK;AAAA,IACZ;AA5FE,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO;AAEZ,SAAK,oBAAoB;AAAA,MACvB,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,IACL;AAEA,SAAK,QAAQ,WAAW;AAExB,QAAI,YAAY;AACd,WAAK,aAAa;AAAA,IACpB;AAEA,SAAK,QAAQ,iBAAiB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAK,CAAC;AACnF,SAAK,OAAO,GAAG,SAAS,KAAK,YAAY;AACzC,SAAK,OAAO,GAAG,QAAQ,KAAK,WAAW;AAEvC,SAAK,OAAO,MAAM,KAAK,KAAK;AAE5B,QAAI,KAAK,cAAc,GAAG;AACxB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EA7GQ,eAAe,MAAuB;AAC5C,eAAO,qBAAQ,MAAM,EAAE,qBAAiB,0CAA6B,KAAK,OAAO,MAAM,EAAE,CAAC;AAAA,EAC5F;AAAA,EAkCA,IAAI,cAAc;AAChB,UAAM,cAA4B,CAAC;AAEnC,QAAI,KAAK,kBAAkB,MAAM;AAC/B,kBAAY,SAAK,iBAAK,OAAO,KAAK,kBAAkB,SAAS,YAAY,KAAK,kBAAkB,OAAO,MAAS,CAAC;AAAA,IACnH;AAEA,QAAI,KAAK,kBAAkB,OAAO;AAChC,kBAAY;AAAA,YACV,kBAAM,OAAO,KAAK,kBAAkB,UAAU,YAAY,KAAK,kBAAkB,QAAQ,MAAS;AAAA,MACpG;AAAA,IACF;AAEA,QAAI,KAAK,kBAAkB,QAAQ;AACjC,kBAAY;AAAA,YACV,mBAAO,OAAO,KAAK,kBAAkB,WAAW,YAAY,KAAK,kBAAkB,SAAS,MAAS;AAAA,MACvG;AAAA,IACF;AAEA,QAAI,KAAK,kBAAkB,OAAO;AAChC,kBAAY,SAAK,kBAAM,KAAK,kBAAkB,KAAK,CAAC;AAAA,IACtD;AAEA,QAAI,KAAK,kBAAkB,MAAM;AAC/B,kBAAY,SAAK,iBAAK,OAAO,KAAK,kBAAkB,SAAS,YAAY,KAAK,kBAAkB,OAAO,MAAS,CAAC;AAAA,IACnH;AAEA,QAAI,KAAK,kBAAkB,eAAe;AACxC,kBAAY;AAAA,YACV;AAAA,UACE,OAAO,KAAK,kBAAkB,kBAAkB,YAAY,KAAK,kBAAkB,gBAAgB;AAAA,QACrG;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,kBAAkB,MAAM;AAC/B,kBAAY,SAAK,iBAAK,OAAO,KAAK,kBAAkB,SAAS,YAAY,KAAK,kBAAkB,OAAO,MAAS,CAAC;AAAA,IACnH;AAEA,QAAI,KAAK,kBAAkB,QAAQ;AACjC,kBAAY;AAAA,YACV,mBAAO,OAAO,KAAK,kBAAkB,WAAW,YAAY,KAAK,kBAAkB,SAAS,MAAS;AAAA,MACvG;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EA6BA,cAAc,UAAwB;AA3NxC;AA4NI,UAAM,EAAE,MAAM,IAAI,KAAK;AACvB,UAAM,EAAE,UAAU,IAAI;AAEtB,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,OAAO,KAAK,IAAI,GAAG,OAAO,IAAI,WAAS,MAAM,MAAM,GAAG,CAAC;AAC7D,UAAM,KAAK,KAAK,IAAI,GAAG,OAAO,IAAI,WAAS,MAAM,IAAI,GAAG,CAAC;AAEzD,UAAM,cAAa,UAAK,eAAL,8BAAkB;AAAA,MACnC,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAkDA,iBAAiB;AACf,UAAM,EAAE,UAAU,IAAI,KAAK,OAAO;AAElC,UAAM,cAAU,0BAAa,KAAK,MAAM,UAAU,MAAM,UAAU,EAAE;AAEpE,UAAM,iBAAiB;AAAA,MACrB,uBAAuB,MAAM;AAAA,MAC7B,gBAAgB,MAAM,CAAC,OAAO;AAAA,IAChC;AAEA,oCAAgB,gBAAgB,KAAK,SAAS;AAAA,MAC5C,WAAW,KAAK,kBAAkB;AAAA,MAClC,UAAU,KAAK,kBAAkB;AAAA,MACjC,YAAY,KAAK;AAAA,IACnB,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,GAAG,SAAS,MAAM;AAC9B,WAAK,QAAQ,MAAM,QAAQ;AAC3B,WAAK,QAAQ,MAAM,WAAW;AAC9B,WAAK,QAAQ,MAAM,OAAO,GAAG,CAAC;AAC9B,WAAK,QAAQ,MAAM,MAAM,GAAG,CAAC;AAE7B,UAAI,KAAK,aAAa,KAAK,kBAAkB,UAAU;AACrD,aAAK,kBAAkB,SAAS;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,OAAO,MAAkB,UAAwB;AAC/C,UAAM,mBAAmB,EAAC,qCAAU,UAAU,GAAG,KAAK,MAAM;AAC5D,UAAM,aAAa,EAAC,qCAAU,IAAI,GAAG,KAAK,MAAM;AAEhD,SAAK,cAAc,MAAM,kBAAkB,YAAY,QAAQ;AAAA,EACjE;AAAA,EAEA,OAAO;AAhUT;AAiUI,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AAEA,SAAK,QAAQ,MAAM,aAAa;AAChC,SAAK,QAAQ,MAAM,UAAU;AAE7B,eAAK,KAAK,IAAI,kBAAd,mBAA6B,YAAY,KAAK;AAE9C,QAAI,KAAK,kBAAkB,QAAQ;AACjC,WAAK,kBAAkB,OAAO;AAAA,IAChC;AAEA,SAAK,YAAY;AAAA,EACnB;AAAA,EAEA,OAAO;AACL,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AAEA,SAAK,QAAQ,MAAM,aAAa;AAChC,SAAK,QAAQ,MAAM,UAAU;AAE7B,SAAK,QAAQ,OAAO;AAEpB,QAAI,KAAK,kBAAkB,QAAQ;AACjC,WAAK,kBAAkB,OAAO;AAAA,IAChC;AAEA,SAAK,YAAY;AAAA,EACnB;AAAA,EAEA,UAAU;AACR,SAAK,KAAK;AACV,SAAK,QAAQ,oBAAoB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAK,CAAC;AACtF,SAAK,OAAO,IAAI,SAAS,KAAK,YAAY;AAC1C,SAAK,OAAO,IAAI,QAAQ,KAAK,WAAW;AAExC,QAAI,KAAK,kBAAkB,WAAW;AACpC,WAAK,kBAAkB,UAAU;AAAA,IACnC;AAAA,EACF;AACF;AAEO,IAAM,qBAAqB,CAAC,YAAqC;AACtE,SAAO,IAAI,oBAAO;AAAA,IAChB,KAAK,OAAO,QAAQ,cAAc,WAAW,IAAI,uBAAU,QAAQ,SAAS,IAAI,QAAQ;AAAA,IACxF,MAAM,UAAQ,IAAI,iBAAiB,EAAE,MAAM,GAAG,QAAQ,CAAC;AAAA,EACzD,CAAC;AACH;;;ADjWO,IAAM,eAAe,uBAAU,OAA4B;AAAA,EAChE,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,SAAS;AAAA,MACT,SAAS,CAAC;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EAEA,wBAAwB;AACtB,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAO,CAAC;AAAA,IACV;AAEA,WAAO;AAAA,MACL,mBAAmB;AAAA,QACjB,WAAW,KAAK,QAAQ;AAAA,QACxB,QAAQ,KAAK;AAAA,QACb,SAAS,KAAK,QAAQ;AAAA,QACtB,SAAS,KAAK,QAAQ;AAAA,QACtB,YAAY,KAAK,QAAQ;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ADxCD,IAAO,gBAAQ;", "names": ["import_core"]}