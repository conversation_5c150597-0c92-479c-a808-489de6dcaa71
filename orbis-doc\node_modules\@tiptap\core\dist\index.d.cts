import { Transaction, EditorState, Plugin, Selection, NodeSelection, TextSelection, PluginKey } from '@tiptap/pm/state';
import { Node as Node$1, MarkType as MarkType$1, MarkSpec, Mark as Mark$1, DOMOutputSpec, NodeType as NodeType$1, NodeSpec, Slice, ParseOptions, Fragment as Fragment$1, <PERSON>hem<PERSON>, ContentMatch, ResolvedPos, ParseRule } from '@tiptap/pm/model';
import { EditorProps, EditorView, Decoration, DecorationAttrs, ViewMutationRecord, NodeViewConstructor, NodeView as NodeView$1, MarkViewConstructor, MarkView as MarkView$1 } from '@tiptap/pm/view';
import { Transform, Mappable } from '@tiptap/pm/transform';

type StringKeyOf<T> = Extract<keyof T, string>;
type CallbackType<T extends Record<string, any>, EventName extends StringKeyOf<T>> = T[EventName] extends any[] ? T[EventName] : [T[EventName]];
type CallbackFunction<T extends Record<string, any>, EventName extends StringKeyOf<T>> = (...props: CallbackType<T, EventName>) => any;
declare class EventEmitter<T extends Record<string, any>> {
    private callbacks;
    on<EventName extends StringKeyOf<T>>(event: EventName, fn: CallbackFunction<T, EventName>): this;
    emit<EventName extends StringKeyOf<T>>(event: EventName, ...args: CallbackType<T, EventName>): this;
    off<EventName extends StringKeyOf<T>>(event: EventName, fn?: CallbackFunction<T, EventName>): this;
    once<EventName extends StringKeyOf<T>>(event: EventName, fn: CallbackFunction<T, EventName>): this;
    removeAllListeners(): void;
}

/**
 * Returns a new `Transform` based on all steps of the passed transactions.
 * @param oldDoc The Prosemirror node to start from
 * @param transactions The transactions to combine
 * @returns A new `Transform` with all steps of the passed transactions
 */
declare function combineTransactionSteps(oldDoc: Node$1, transactions: Transaction[]): Transform;

/**
 * Takes a Transaction & Editor State and turns it into a chainable state object
 * @param config The transaction and state to create the chainable state from
 * @returns A chainable Editor state object
 */
declare function createChainableState(config: {
    transaction: Transaction;
    state: EditorState;
}): EditorState;

type InputRuleMatch = {
    index: number;
    text: string;
    replaceWith?: string;
    match?: RegExpMatchArray;
    data?: Record<string, any>;
};
type InputRuleFinder = RegExp | ((text: string) => InputRuleMatch | null);
declare class InputRule {
    find: InputRuleFinder;
    handler: (props: {
        state: EditorState;
        range: Range;
        match: ExtendedRegExpMatchArray;
        commands: SingleCommands;
        chain: () => ChainedCommands;
        can: () => CanCommands;
    }) => void | null;
    constructor(config: {
        find: InputRuleFinder;
        handler: (props: {
            state: EditorState;
            range: Range;
            match: ExtendedRegExpMatchArray;
            commands: SingleCommands;
            chain: () => ChainedCommands;
            can: () => CanCommands;
        }) => void | null;
    });
}
/**
 * Create an input rules plugin. When enabled, it will cause text
 * input that matches any of the given rules to trigger the rule’s
 * action.
 */
declare function inputRulesPlugin(props: {
    editor: Editor;
    rules: InputRule[];
}): Plugin;

interface MarkConfig<Options = any, Storage = any> extends ExtendableConfig<Options, Storage, MarkConfig<Options, Storage>, MarkType$1> {
    /**
     * Mark View
     */
    addMarkView?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: MarkType$1;
        parent: ParentConfig<MarkConfig<Options, Storage>>['addMarkView'];
    }) => MarkViewRenderer) | null;
    /**
     * Keep mark after split node
     */
    keepOnSplit?: boolean | (() => boolean);
    /**
     * Inclusive
     */
    inclusive?: MarkSpec['inclusive'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<MarkConfig<Options, Storage>>['inclusive'];
        editor?: Editor;
    }) => MarkSpec['inclusive']);
    /**
     * Excludes
     */
    excludes?: MarkSpec['excludes'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<MarkConfig<Options, Storage>>['excludes'];
        editor?: Editor;
    }) => MarkSpec['excludes']);
    /**
     * Marks this Mark as exitable
     */
    exitable?: boolean | (() => boolean);
    /**
     * Group
     */
    group?: MarkSpec['group'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<MarkConfig<Options, Storage>>['group'];
        editor?: Editor;
    }) => MarkSpec['group']);
    /**
     * Spanning
     */
    spanning?: MarkSpec['spanning'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<MarkConfig<Options, Storage>>['spanning'];
        editor?: Editor;
    }) => MarkSpec['spanning']);
    /**
     * Code
     */
    code?: boolean | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<MarkConfig<Options, Storage>>['code'];
        editor?: Editor;
    }) => boolean);
    /**
     * Parse HTML
     */
    parseHTML?: (this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<MarkConfig<Options, Storage>>['parseHTML'];
        editor?: Editor;
    }) => MarkSpec['parseDOM'];
    /**
     * Render HTML
     */
    renderHTML?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<MarkConfig<Options, Storage>>['renderHTML'];
        editor?: Editor;
    }, props: {
        mark: Mark$1;
        HTMLAttributes: Record<string, any>;
    }) => DOMOutputSpec) | null;
    /**
     * Attributes
     */
    addAttributes?: (this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<MarkConfig<Options, Storage>>['addAttributes'];
        editor?: Editor;
    }) => Attributes$1 | {};
}
/**
 * The Mark class is used to create custom mark extensions.
 * @see https://tiptap.dev/api/extensions#create-a-new-extension
 */
declare class Mark<Options = any, Storage = any> extends Extendable<Options, Storage, MarkConfig<Options, Storage>> {
    type: string;
    /**
     * Create a new Mark instance
     * @param config - Mark configuration object or a function that returns a configuration object
     */
    static create<O = any, S = any>(config?: Partial<MarkConfig<O, S>> | (() => Partial<MarkConfig<O, S>>)): Mark<O, S>;
    static handleExit({ editor, mark }: {
        editor: Editor;
        mark: Mark;
    }): boolean;
    configure(options?: Partial<Options>): Mark<Options, Storage>;
    extend<ExtendedOptions = Options, ExtendedStorage = Storage, ExtendedConfig = MarkConfig<ExtendedOptions, ExtendedStorage>>(extendedConfig?: (() => Partial<ExtendedConfig>) | (Partial<ExtendedConfig> & ThisType<{
        name: string;
        options: ExtendedOptions;
        storage: ExtendedStorage;
        editor: Editor;
        type: MarkType$1;
    }>)): Mark<ExtendedOptions, ExtendedStorage>;
}

interface NodeConfig<Options = any, Storage = any> extends ExtendableConfig<Options, Storage, NodeConfig<Options, Storage>, NodeType$1> {
    /**
     * Node View
     */
    addNodeView?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: NodeType$1;
        parent: ParentConfig<NodeConfig<Options, Storage>>['addNodeView'];
    }) => NodeViewRenderer) | null;
    /**
     * Defines if this node should be a top level node (doc)
     * @default false
     * @example true
     */
    topNode?: boolean;
    /**
     * The content expression for this node, as described in the [schema
     * guide](/docs/guide/#schema.content_expressions). When not given,
     * the node does not allow any content.
     *
     * You can read more about it on the Prosemirror documentation here
     * @see https://prosemirror.net/docs/guide/#schema.content_expressions
     * @default undefined
     * @example content: 'block+'
     * @example content: 'headline paragraph block*'
     */
    content?: NodeSpec['content'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['content'];
        editor?: Editor;
    }) => NodeSpec['content']);
    /**
     * The marks that are allowed inside of this node. May be a
     * space-separated string referring to mark names or groups, `"_"`
     * to explicitly allow all marks, or `""` to disallow marks. When
     * not given, nodes with inline content default to allowing all
     * marks, other nodes default to not allowing marks.
     *
     * @example marks: 'strong em'
     */
    marks?: NodeSpec['marks'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['marks'];
        editor?: Editor;
    }) => NodeSpec['marks']);
    /**
     * The group or space-separated groups to which this node belongs,
     * which can be referred to in the content expressions for the
     * schema.
     *
     * By default Tiptap uses the groups 'block' and 'inline' for nodes. You
     * can also use custom groups if you want to group specific nodes together
     * and handle them in your schema.
     * @example group: 'block'
     * @example group: 'inline'
     * @example group: 'customBlock' // this uses a custom group
     */
    group?: NodeSpec['group'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['group'];
        editor?: Editor;
    }) => NodeSpec['group']);
    /**
     * Should be set to true for inline nodes. (Implied for text nodes.)
     */
    inline?: NodeSpec['inline'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['inline'];
        editor?: Editor;
    }) => NodeSpec['inline']);
    /**
     * Can be set to true to indicate that, though this isn't a [leaf
     * node](https://prosemirror.net/docs/ref/#model.NodeType.isLeaf), it doesn't have directly editable
     * content and should be treated as a single unit in the view.
     *
     * @example atom: true
     */
    atom?: NodeSpec['atom'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['atom'];
        editor?: Editor;
    }) => NodeSpec['atom']);
    /**
     * Controls whether nodes of this type can be selected as a [node
     * selection](https://prosemirror.net/docs/ref/#state.NodeSelection). Defaults to true for non-text
     * nodes.
     *
     * @default true
     * @example selectable: false
     */
    selectable?: NodeSpec['selectable'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['selectable'];
        editor?: Editor;
    }) => NodeSpec['selectable']);
    /**
     * Determines whether nodes of this type can be dragged without
     * being selected. Defaults to false.
     *
     * @default: false
     * @example: draggable: true
     */
    draggable?: NodeSpec['draggable'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['draggable'];
        editor?: Editor;
    }) => NodeSpec['draggable']);
    /**
     * Can be used to indicate that this node contains code, which
     * causes some commands to behave differently.
     */
    code?: NodeSpec['code'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['code'];
        editor?: Editor;
    }) => NodeSpec['code']);
    /**
     * Controls way whitespace in this a node is parsed. The default is
     * `"normal"`, which causes the [DOM parser](https://prosemirror.net/docs/ref/#model.DOMParser) to
     * collapse whitespace in normal mode, and normalize it (replacing
     * newlines and such with spaces) otherwise. `"pre"` causes the
     * parser to preserve spaces inside the node. When this option isn't
     * given, but [`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) is true, `whitespace`
     * will default to `"pre"`. Note that this option doesn't influence
     * the way the node is rendered—that should be handled by `toDOM`
     * and/or styling.
     */
    whitespace?: NodeSpec['whitespace'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['whitespace'];
        editor?: Editor;
    }) => NodeSpec['whitespace']);
    /**
     * Allows a **single** node to be set as linebreak equivalent (e.g. hardBreak).
     * When converting between block types that have whitespace set to "pre"
     * and don't support the linebreak node (e.g. codeBlock) and other block types
     * that do support the linebreak node (e.g. paragraphs) - this node will be used
     * as the linebreak instead of stripping the newline.
     *
     * See [linebreakReplacement](https://prosemirror.net/docs/ref/#model.NodeSpec.linebreakReplacement).
     */
    linebreakReplacement?: NodeSpec['linebreakReplacement'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['linebreakReplacement'];
        editor?: Editor;
    }) => NodeSpec['linebreakReplacement']);
    /**
     * When enabled, enables both
     * [`definingAsContext`](https://prosemirror.net/docs/ref/#model.NodeSpec.definingAsContext) and
     * [`definingForContent`](https://prosemirror.net/docs/ref/#model.NodeSpec.definingForContent).
     *
     * @default false
     * @example isolating: true
     */
    defining?: NodeSpec['defining'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['defining'];
        editor?: Editor;
    }) => NodeSpec['defining']);
    /**
     * When enabled (default is false), the sides of nodes of this type
     * count as boundaries that regular editing operations, like
     * backspacing or lifting, won't cross. An example of a node that
     * should probably have this enabled is a table cell.
     */
    isolating?: NodeSpec['isolating'] | ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['isolating'];
        editor?: Editor;
    }) => NodeSpec['isolating']);
    /**
     * Associates DOM parser information with this node, which can be
     * used by [`DOMParser.fromSchema`](https://prosemirror.net/docs/ref/#model.DOMParser^fromSchema) to
     * automatically derive a parser. The `node` field in the rules is
     * implied (the name of this node will be filled in automatically).
     * If you supply your own parser, you do not need to also specify
     * parsing rules in your schema.
     *
     * @example parseHTML: [{ tag: 'div', attrs: { 'data-id': 'my-block' } }]
     */
    parseHTML?: (this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['parseHTML'];
        editor?: Editor;
    }) => NodeSpec['parseDOM'];
    /**
     * A description of a DOM structure. Can be either a string, which is
     * interpreted as a text node, a DOM node, which is interpreted as
     * itself, a `{dom, contentDOM}` object, or an array.
     *
     * An array describes a DOM element. The first value in the array
     * should be a string—the name of the DOM element, optionally prefixed
     * by a namespace URL and a space. If the second element is plain
     * object, it is interpreted as a set of attributes for the element.
     * Any elements after that (including the 2nd if it's not an attribute
     * object) are interpreted as children of the DOM elements, and must
     * either be valid `DOMOutputSpec` values, or the number zero.
     *
     * The number zero (pronounced “hole”) is used to indicate the place
     * where a node's child nodes should be inserted. If it occurs in an
     * output spec, it should be the only child element in its parent
     * node.
     *
     * @example toDOM: ['div[data-id="my-block"]', { class: 'my-block' }, 0]
     */
    renderHTML?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['renderHTML'];
        editor?: Editor;
    }, props: {
        node: Node$1;
        HTMLAttributes: Record<string, any>;
    }) => DOMOutputSpec) | null;
    /**
     * renders the node as text
     * @example renderText: () => 'foo
     */
    renderText?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['renderText'];
        editor?: Editor;
    }, props: {
        node: Node$1;
        pos: number;
        parent: Node$1;
        index: number;
    }) => string) | null;
    /**
     * Add attributes to the node
     * @example addAttributes: () => ({ class: 'foo' })
     */
    addAttributes?: (this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<NodeConfig<Options, Storage>>['addAttributes'];
        editor?: Editor;
    }) => Attributes$1 | {};
}
/**
 * The Node class is used to create custom node extensions.
 * @see https://tiptap.dev/api/extensions#create-a-new-extension
 */
declare class Node<Options = any, Storage = any> extends Extendable<Options, Storage, NodeConfig<Options, Storage>> {
    type: string;
    /**
     * Create a new Node instance
     * @param config - Node configuration object or a function that returns a configuration object
     */
    static create<O = any, S = any>(config?: Partial<NodeConfig<O, S>> | (() => Partial<NodeConfig<O, S>>)): Node<O, S>;
    configure(options?: Partial<Options>): Node<Options, Storage>;
    extend<ExtendedOptions = Options, ExtendedStorage = Storage, ExtendedConfig = NodeConfig<ExtendedOptions, ExtendedStorage>>(extendedConfig?: (() => Partial<ExtendedConfig>) | (Partial<ExtendedConfig> & ThisType<{
        name: string;
        options: ExtendedOptions;
        storage: ExtendedStorage;
        editor: Editor;
        type: NodeType$1;
    }>)): Node<ExtendedOptions, ExtendedStorage>;
}

type PasteRuleMatch = {
    index: number;
    text: string;
    replaceWith?: string;
    match?: RegExpMatchArray;
    data?: Record<string, any>;
};
type PasteRuleFinder = RegExp | ((text: string, event?: ClipboardEvent | null) => PasteRuleMatch[] | null | undefined);
/**
 * Paste rules are used to react to pasted content.
 * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules
 */
declare class PasteRule {
    find: PasteRuleFinder;
    handler: (props: {
        state: EditorState;
        range: Range;
        match: ExtendedRegExpMatchArray;
        commands: SingleCommands;
        chain: () => ChainedCommands;
        can: () => CanCommands;
        pasteEvent: ClipboardEvent | null;
        dropEvent: DragEvent | null;
    }) => void | null;
    constructor(config: {
        find: PasteRuleFinder;
        handler: (props: {
            can: () => CanCommands;
            chain: () => ChainedCommands;
            commands: SingleCommands;
            dropEvent: DragEvent | null;
            match: ExtendedRegExpMatchArray;
            pasteEvent: ClipboardEvent | null;
            range: Range;
            state: EditorState;
        }) => void | null;
    });
}
/**
 * Create an paste rules plugin. When enabled, it will cause pasted
 * text that matches any of the given rules to trigger the rule’s
 * action.
 */
declare function pasteRulesPlugin(props: {
    editor: Editor;
    rules: PasteRule[];
}): Plugin[];

interface ExtendableConfig<Options = any, Storage = any, Config extends ExtensionConfig<Options, Storage> | NodeConfig<Options, Storage> | MarkConfig<Options, Storage> | ExtendableConfig<Options, Storage> = ExtendableConfig<Options, Storage, any, any>, PMType = any> {
    /**
     * The extension name - this must be unique.
     * It will be used to identify the extension.
     *
     * @example 'myExtension'
     */
    name: string;
    /**
     * The priority of your extension. The higher, the earlier it will be called
     * and will take precedence over other extensions with a lower priority.
     * @default 100
     * @example 101
     */
    priority?: number;
    /**
     * This method will add options to this extension
     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#settings
     * @example
     * addOptions() {
     *  return {
     *    myOption: 'foo',
     *    myOtherOption: 10,
     * }
     */
    addOptions?: (this: {
        name: string;
        parent: ParentConfig<Config>['addOptions'];
    }) => Options;
    /**
     * The default storage this extension can save data to.
     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#storage
     * @example
     * defaultStorage: {
     *   prefetchedUsers: [],
     *   loading: false,
     * }
     */
    addStorage?: (this: {
        name: string;
        options: Options;
        parent: ParentConfig<Config>['addStorage'];
    }) => Storage;
    /**
     * This function adds globalAttributes to specific nodes.
     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#global-attributes
     * @example
     * addGlobalAttributes() {
     *   return [
     *     {
             // Extend the following extensions
     *       types: [
     *         'heading',
     *         'paragraph',
     *       ],
     *       // … with those attributes
     *       attributes: {
     *         textAlign: {
     *           default: 'left',
     *           renderHTML: attributes => ({
     *             style: `text-align: ${attributes.textAlign}`,
     *           }),
     *           parseHTML: element => element.style.textAlign || 'left',
     *         },
     *       },
     *     },
     *   ]
     * }
     */
    addGlobalAttributes?: (this: {
        name: string;
        options: Options;
        storage: Storage;
        extensions: (Node | Mark)[];
        parent: ParentConfig<Config>['addGlobalAttributes'];
    }) => GlobalAttributes;
    /**
     * This function adds commands to the editor
     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#commands
     * @example
     * addCommands() {
     *   return {
     *     myCommand: () => ({ chain }) => chain().setMark('type', 'foo').run(),
     *   }
     * }
     */
    addCommands?: (this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['addCommands'];
    }) => Partial<RawCommands>;
    /**
     * This function registers keyboard shortcuts.
     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#keyboard-shortcuts
     * @example
     * addKeyboardShortcuts() {
     *   return {
     *     'Mod-l': () => this.editor.commands.toggleBulletList(),
     *   }
     * },
     */
    addKeyboardShortcuts?: (this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['addKeyboardShortcuts'];
    }) => {
        [key: string]: KeyboardShortcutCommand;
    };
    /**
     * This function adds input rules to the editor.
     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#input-rules
     * @example
     * addInputRules() {
     *   return [
     *     markInputRule({
     *       find: inputRegex,
     *       type: this.type,
     *     }),
     *   ]
     * },
     */
    addInputRules?: (this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['addInputRules'];
    }) => InputRule[];
    /**
     * This function adds paste rules to the editor.
     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#paste-rules
     * @example
     * addPasteRules() {
     *   return [
     *     markPasteRule({
     *       find: pasteRegex,
     *       type: this.type,
     *     }),
     *   ]
     * },
     */
    addPasteRules?: (this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['addPasteRules'];
    }) => PasteRule[];
    /**
     * This function adds Prosemirror plugins to the editor
     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#prosemirror-plugins
     * @example
     * addProseMirrorPlugins() {
     *   return [
     *     customPlugin(),
     *   ]
     * }
     */
    addProseMirrorPlugins?: (this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['addProseMirrorPlugins'];
    }) => Plugin[];
    /**
     * This function adds additional extensions to the editor. This is useful for
     * building extension kits.
     * @example
     * addExtensions() {
     *   return [
     *     BulletList,
     *     OrderedList,
     *     ListItem
     *   ]
     * }
     */
    addExtensions?: (this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<Config>['addExtensions'];
    }) => Extensions;
    /**
     * This function extends the schema of the node.
     * @example
     * extendNodeSchema() {
     *   return {
     *     group: 'inline',
     *     selectable: false,
     *   }
     * }
     */
    extendNodeSchema?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<Config>['extendNodeSchema'];
    }, extension: Node) => Record<string, any>) | null;
    /**
     * This function extends the schema of the mark.
     * @example
     * extendMarkSchema() {
     *   return {
     *     group: 'inline',
     *     selectable: false,
     *   }
     * }
     */
    extendMarkSchema?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        parent: ParentConfig<Config>['extendMarkSchema'];
    }, extension: Mark) => Record<string, any>) | null;
    /**
     * The editor is not ready yet.
     */
    onBeforeCreate?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['onBeforeCreate'];
    }, event: EditorEvents['beforeCreate']) => void) | null;
    /**
     * The editor is ready.
     */
    onCreate?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['onCreate'];
    }, event: EditorEvents['create']) => void) | null;
    /**
     * The content has changed.
     */
    onUpdate?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['onUpdate'];
    }, event: EditorEvents['update']) => void) | null;
    /**
     * The selection has changed.
     */
    onSelectionUpdate?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['onSelectionUpdate'];
    }, event: EditorEvents['selectionUpdate']) => void) | null;
    /**
     * The editor state has changed.
     */
    onTransaction?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['onTransaction'];
    }, event: EditorEvents['transaction']) => void) | null;
    /**
     * The editor is focused.
     */
    onFocus?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['onFocus'];
    }, event: EditorEvents['focus']) => void) | null;
    /**
     * The editor isn’t focused anymore.
     */
    onBlur?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['onBlur'];
    }, event: EditorEvents['blur']) => void) | null;
    /**
     * The editor is destroyed.
     */
    onDestroy?: ((this: {
        name: string;
        options: Options;
        storage: Storage;
        editor: Editor;
        type: PMType;
        parent: ParentConfig<Config>['onDestroy'];
    }, event: EditorEvents['destroy']) => void) | null;
}
declare class Extendable<Options = any, Storage = any, Config = ExtensionConfig<Options, Storage> | NodeConfig<Options, Storage> | MarkConfig<Options, Storage>> {
    type: string;
    parent: Extendable | null;
    child: Extendable | null;
    name: string;
    config: Config;
    constructor(config?: Partial<Config>);
    get options(): Options;
    get storage(): Readonly<Storage>;
    configure(options?: Partial<Options>): Extendable<Options, Storage, ExtensionConfig<Options, Storage> | NodeConfig<Options, Storage> | MarkConfig<Options, Storage>>;
    extend<ExtendedOptions = Options, ExtendedStorage = Storage, ExtendedConfig = ExtensionConfig<ExtendedOptions, ExtendedStorage> | NodeConfig<ExtendedOptions, ExtendedStorage> | MarkConfig<ExtendedOptions, ExtendedStorage>>(extendedConfig?: Partial<ExtendedConfig>): Extendable<ExtendedOptions, ExtendedStorage>;
}

type AnyConfig = ExtensionConfig | NodeConfig | MarkConfig;
type AnyExtension = Extendable;
type Extensions = AnyExtension[];
type ParentConfig<T> = Partial<{
    [P in keyof T]: Required<T>[P] extends (...args: any) => any ? (...args: Parameters<Required<T>[P]>) => ReturnType<Required<T>[P]> : T[P];
}>;
type Primitive = null | undefined | string | number | boolean | symbol | bigint;
type RemoveThis<T> = T extends (...args: any) => any ? (...args: Parameters<T>) => ReturnType<T> : T;
type MaybeReturnType<T> = T extends (...args: any) => any ? ReturnType<T> : T;
type MaybeThisParameterType<T> = Exclude<T, Primitive> extends (...args: any) => any ? ThisParameterType<Exclude<T, Primitive>> : any;
interface EditorEvents {
    beforeCreate: {
        /**
         * The editor instance
         */
        editor: Editor;
    };
    create: {
        /**
         * The editor instance
         */
        editor: Editor;
    };
    contentError: {
        /**
         * The editor instance
         */
        editor: Editor;
        /**
         * The error that occurred while parsing the content
         */
        error: Error;
        /**
         * If called, will re-initialize the editor with the collaboration extension removed.
         * This will prevent syncing back deletions of content not present in the current schema.
         */
        disableCollaboration: () => void;
    };
    update: {
        /**
         * The editor instance
         */
        editor: Editor;
        /**
         * The transaction that caused the update
         */
        transaction: Transaction;
        /**
         * Appended transactions that were added to the initial transaction by plugins
         */
        appendedTransactions: Transaction[];
    };
    selectionUpdate: {
        /**
         * The editor instance
         */
        editor: Editor;
        /**
         * The transaction that caused the selection update
         */
        transaction: Transaction;
    };
    beforeTransaction: {
        /**
         * The editor instance
         */
        editor: Editor;
        /**
         * The transaction that will be applied
         */
        transaction: Transaction;
        /**
         * The next state of the editor after the transaction is applied
         */
        nextState: EditorState;
    };
    transaction: {
        /**
         * The editor instance
         */
        editor: Editor;
        /**
         * The initial transaction
         */
        transaction: Transaction;
        /**
         * Appended transactions that were added to the initial transaction by plugins
         */
        appendedTransactions: Transaction[];
    };
    focus: {
        /**
         * The editor instance
         */
        editor: Editor;
        /**
         * The focus event
         */
        event: FocusEvent;
        /**
         * The transaction that caused the focus
         */
        transaction: Transaction;
    };
    blur: {
        /**
         * The editor instance
         */
        editor: Editor;
        /**
         * The focus event
         */
        event: FocusEvent;
        /**
         * The transaction that caused the blur
         */
        transaction: Transaction;
    };
    destroy: void;
    paste: {
        /**
         * The editor instance
         */
        editor: Editor;
        /**
         * The clipboard event
         */
        event: ClipboardEvent;
        /**
         * The slice that was pasted
         */
        slice: Slice;
    };
    drop: {
        /**
         * The editor instance
         */
        editor: Editor;
        /**
         * The drag event
         */
        event: DragEvent;
        /**
         * The slice that was dropped
         */
        slice: Slice;
        /**
         * Whether the content was moved (true) or copied (false)
         */
        moved: boolean;
    };
    delete: {
        /**
         * The editor instance
         */
        editor: Editor;
        /**
         * The range of the deleted content (before the deletion)
         */
        deletedRange: Range;
        /**
         * The new range of positions of where the deleted content was in the new document (after the deletion)
         */
        newRange: Range;
        /**
         * The transaction that caused the deletion
         */
        transaction: Transaction;
        /**
         * The combined transform (including all appended transactions) that caused the deletion
         */
        combinedTransform: Transform;
        /**
         * Whether the deletion was partial (only a part of this content was deleted)
         */
        partial: boolean;
        /**
         * This is the start position of the mark in the document (before the deletion)
         */
        from: number;
        /**
         * This is the end position of the mark in the document (before the deletion)
         */
        to: number;
    } & ({
        /**
         * The content that was deleted
         */
        type: 'node';
        /**
         * The node which the deletion occurred in
         * @note This can be a parent node of the deleted content
         */
        node: Node$1;
        /**
         * The new start position of the node in the document (after the deletion)
         */
        newFrom: number;
        /**
         * The new end position of the node in the document (after the deletion)
         */
        newTo: number;
    } | {
        /**
         * The content that was deleted
         */
        type: 'mark';
        /**
         * The mark that was deleted
         */
        mark: Mark$1;
    });
}
type EnableRules = (AnyExtension | string)[] | boolean;
interface EditorOptions {
    /**
     * The element or selector to bind the editor to
     * If `null` is passed, the editor will not be mounted automatically
     * If a function is passed, it will be called with the editor's root element
     */
    element: Element | null;
    /**
     * The content of the editor (HTML, JSON, or a JSON array)
     */
    content: Content;
    /**
     * The extensions to use
     */
    extensions: Extensions;
    /**
     * Whether to inject base CSS styles
     */
    injectCSS: boolean;
    /**
     * A nonce to use for CSP while injecting styles
     */
    injectNonce: string | undefined;
    /**
     * The editor's initial focus position
     */
    autofocus: FocusPosition;
    /**
     * Whether the editor is editable
     */
    editable: boolean;
    /**
     * The editor's props
     */
    editorProps: EditorProps;
    /**
     * The editor's content parser options
     */
    parseOptions: ParseOptions;
    /**
     * The editor's core extension options
     */
    coreExtensionOptions?: {
        clipboardTextSerializer?: {
            blockSeparator?: string;
        };
        delete?: {
            /**
             * Whether the `delete` extension should be called asynchronously to avoid blocking the editor while processing deletions
             * @default true deletion events are called asynchronously
             */
            async?: boolean;
            /**
             * Allows filtering the transactions that are processed by the `delete` extension.
             * If the function returns `true`, the transaction will be ignored.
             */
            filterTransaction?: (transaction: Transaction) => boolean;
        };
    };
    /**
     * Whether to enable input rules behavior
     */
    enableInputRules: EnableRules;
    /**
     * Whether to enable paste rules behavior
     */
    enablePasteRules: EnableRules;
    /**
     * Determines whether core extensions are enabled.
     *
     * If set to `false`, all core extensions will be disabled.
     * To disable specific core extensions, provide an object where the keys are the extension names and the values are `false`.
     * Extensions not listed in the object will remain enabled.
     *
     * @example
     * // Disable all core extensions
     * enabledCoreExtensions: false
     *
     * @example
     * // Disable only the keymap core extension
     * enabledCoreExtensions: { keymap: false }
     *
     * @default true
     */
    enableCoreExtensions?: boolean | Partial<Record<'editable' | 'clipboardTextSerializer' | 'commands' | 'focusEvents' | 'keymap' | 'tabindex' | 'drop' | 'paste' | 'delete', false>>;
    /**
     * If `true`, the editor will check the content for errors on initialization.
     * Emitting the `contentError` event if the content is invalid.
     * Which can be used to show a warning or error message to the user.
     * @default false
     */
    enableContentCheck: boolean;
    /**
     * If `true`, the editor will emit the `contentError` event if invalid content is
     * encountered but `enableContentCheck` is `false`. This lets you preserve the
     * invalid editor content while still showing a warning or error message to
     * the user.
     *
     * @default false
     */
    emitContentError: boolean;
    /**
     * Called before the editor is constructed.
     */
    onBeforeCreate: (props: EditorEvents['beforeCreate']) => void;
    /**
     * Called after the editor is constructed.
     */
    onCreate: (props: EditorEvents['create']) => void;
    /**
     * Called when the editor encounters an error while parsing the content.
     * Only enabled if `enableContentCheck` is `true`.
     */
    onContentError: (props: EditorEvents['contentError']) => void;
    /**
     * Called when the editor's content is updated.
     */
    onUpdate: (props: EditorEvents['update']) => void;
    /**
     * Called when the editor's selection is updated.
     */
    onSelectionUpdate: (props: EditorEvents['selectionUpdate']) => void;
    /**
     * Called after a transaction is applied to the editor.
     */
    onTransaction: (props: EditorEvents['transaction']) => void;
    /**
     * Called on focus events.
     */
    onFocus: (props: EditorEvents['focus']) => void;
    /**
     * Called on blur events.
     */
    onBlur: (props: EditorEvents['blur']) => void;
    /**
     * Called when the editor is destroyed.
     */
    onDestroy: (props: EditorEvents['destroy']) => void;
    /**
     * Called when content is pasted into the editor.
     */
    onPaste: (e: ClipboardEvent, slice: Slice) => void;
    /**
     * Called when content is dropped into the editor.
     */
    onDrop: (e: DragEvent, slice: Slice, moved: boolean) => void;
    /**
     * Called when content is deleted from the editor.
     */
    onDelete: (props: EditorEvents['delete']) => void;
}
/**
 * The editor's content as HTML
 */
type HTMLContent = string;
/**
 * Loosely describes a JSON representation of a Prosemirror document or node
 */
type JSONContent = {
    type?: string;
    attrs?: Record<string, any>;
    content?: JSONContent[];
    marks?: {
        type: string;
        attrs?: Record<string, any>;
        [key: string]: any;
    }[];
    text?: string;
    [key: string]: any;
};
/**
 * A mark type is either a JSON representation of a mark or a Prosemirror mark instance
 */
type MarkType<Type extends string | {
    name: string;
} = any, TAttributes extends undefined | Record<string, any> = any> = {
    type: Type;
    attrs: TAttributes;
};
/**
 * A node type is either a JSON representation of a node or a Prosemirror node instance
 */
type NodeType<Type extends string | {
    name: string;
} = any, TAttributes extends undefined | Record<string, any> = any, NodeMarkType extends MarkType = any, TContent extends (NodeType | TextType)[] = any> = {
    type: Type;
    attrs: TAttributes;
    content?: TContent;
    marks?: NodeMarkType[];
};
/**
 * A node type is either a JSON representation of a doc node or a Prosemirror doc node instance
 */
type DocumentType<TDocAttributes extends Record<string, any> | undefined = Record<string, any>, TContentType extends NodeType[] = NodeType[]> = Omit<NodeType<'doc', TDocAttributes, never, TContentType>, 'marks' | 'content'> & {
    content: TContentType;
};
/**
 * A node type is either a JSON representation of a text node or a Prosemirror text node instance
 */
type TextType<TMarkType extends MarkType = MarkType> = {
    type: 'text';
    text: string;
    marks: TMarkType[];
};
/**
 * Describes the output of a `renderHTML` function in prosemirror
 * @see https://prosemirror.net/docs/ref/#model.DOMOutputSpec
 */
type DOMOutputSpecArray$1 = [string] | [string, Record<string, any>] | [string, 0] | [string, Record<string, any>, 0] | [string, Record<string, any>, DOMOutputSpecArray$1 | 0] | [string, DOMOutputSpecArray$1];
type Content = HTMLContent | JSONContent | JSONContent[] | null;
type CommandProps = {
    editor: Editor;
    tr: Transaction;
    commands: SingleCommands;
    can: () => CanCommands;
    chain: () => ChainedCommands;
    state: EditorState;
    view: EditorView;
    dispatch: ((args?: any) => any) | undefined;
};
type Command = (props: CommandProps) => boolean;
type CommandSpec = (...args: any[]) => Command;
type KeyboardShortcutCommand = (props: {
    editor: Editor;
}) => boolean;
type Attribute = {
    default?: any;
    validate?: string | ((value: any) => void);
    rendered?: boolean;
    renderHTML?: ((attributes: Record<string, any>) => Record<string, any> | null) | null;
    parseHTML?: ((element: HTMLElement) => any | null) | null;
    keepOnSplit?: boolean;
    isRequired?: boolean;
};
type Attributes$1 = {
    [key: string]: Attribute;
};
type ExtensionAttribute = {
    type: string;
    name: string;
    attribute: Required<Omit<Attribute, 'validate'>> & Pick<Attribute, 'validate'>;
};
type GlobalAttributes = {
    /**
     * The node & mark types this attribute should be applied to.
     */
    types: string[];
    /**
     * The attributes to add to the node or mark types.
     */
    attributes: Record<string, Attribute | undefined>;
}[];
type PickValue<T, K extends keyof T> = T[K];
type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never;
type Diff<T extends keyof any, U extends keyof any> = ({
    [P in T]: P;
} & {
    [P in U]: never;
} & {
    [x: string]: never;
})[T];
type Overwrite<T, U> = Pick<T, Diff<keyof T, keyof U>> & U;
type ValuesOf<T> = T[keyof T];
type KeysWithTypeOf<T, Type> = {
    [P in keyof T]: T[P] extends Type ? P : never;
}[keyof T];
type DOMNode = InstanceType<typeof window.Node>;
/**
 * prosemirror-view does not export the `type` property of `Decoration`.
 * So, this defines the `DecorationType` interface to include the `type` property.
 */
interface DecorationType {
    spec: any;
    map(mapping: Mappable, span: Decoration, offset: number, oldOffset: number): Decoration | null;
    valid(node: Node, span: Decoration): boolean;
    eq(other: DecorationType): boolean;
    destroy(dom: DOMNode): void;
    readonly attrs: DecorationAttrs;
}
/**
 * prosemirror-view does not export the `type` property of `Decoration`.
 * This adds the `type` property to the `Decoration` type.
 */
type DecorationWithType = Decoration & {
    type: DecorationType;
};
interface NodeViewProps extends NodeViewRendererProps {
    decorations: readonly DecorationWithType[];
    selected: boolean;
    updateAttributes: (attributes: Record<string, any>) => void;
    deleteNode: () => void;
}
interface NodeViewRendererOptions {
    stopEvent: ((props: {
        event: Event;
    }) => boolean) | null;
    ignoreMutation: ((props: {
        mutation: ViewMutationRecord;
    }) => boolean) | null;
    contentDOMElementTag: string;
}
interface NodeViewRendererProps {
    /**
     * The node that is being rendered.
     */
    node: Parameters<NodeViewConstructor>[0];
    /**
     * The editor's view.
     */
    view: Parameters<NodeViewConstructor>[1];
    /**
     * A function that can be called to get the node's current position in the document.
     */
    getPos: Parameters<NodeViewConstructor>[2];
    /**
     * is an array of node or inline decorations that are active around the node.
     * They are automatically drawn in the normal way, and you will usually just want to ignore this, but they can also be used as a way to provide context information to the node view without adding it to the document itself.
     */
    decorations: Parameters<NodeViewConstructor>[3];
    /**
     * holds the decorations for the node's content. You can safely ignore this if your view has no content or a contentDOM property, since the editor will draw the decorations on the content.
     * But if you, for example, want to create a nested editor with the content, it may make sense to provide it with the inner decorations.
     */
    innerDecorations: Parameters<NodeViewConstructor>[4];
    /**
     * The editor instance.
     */
    editor: Editor;
    /**
     * The extension that is responsible for the node.
     */
    extension: Node;
    /**
     * The HTML attributes that should be added to the node's DOM element.
     */
    HTMLAttributes: Record<string, any>;
}
type NodeViewRenderer = (props: NodeViewRendererProps) => NodeView$1;
interface MarkViewProps extends MarkViewRendererProps {
}
interface MarkViewRendererProps {
    /**
     * The node that is being rendered.
     */
    mark: Parameters<MarkViewConstructor>[0];
    /**
     * The editor's view.
     */
    view: Parameters<MarkViewConstructor>[1];
    /**
     * indicates whether the mark's content is inline
     */
    inline: Parameters<MarkViewConstructor>[2];
    /**
     * The editor instance.
     */
    editor: Editor;
    /**
     * The extension that is responsible for the mark.
     */
    extension: Mark;
    /**
     * The HTML attributes that should be added to the mark's DOM element.
     */
    HTMLAttributes: Record<string, any>;
    updateAttributes: (attrs: Record<string, any>) => void;
}
type MarkViewRenderer<Props = MarkViewRendererProps> = (props: Props) => MarkView$1;
interface MarkViewRendererOptions {
    ignoreMutation: ((props: {
        mutation: ViewMutationRecord;
    }) => boolean) | null;
}
type AnyCommands = Record<string, (...args: any[]) => Command>;
type UnionCommands<T = Command> = UnionToIntersection<ValuesOf<Pick<Commands<T>, KeysWithTypeOf<Commands<T>, object>>>>;
type RawCommands = {
    [Item in keyof UnionCommands]: UnionCommands<Command>[Item];
};
type SingleCommands = {
    [Item in keyof UnionCommands]: UnionCommands<boolean>[Item];
};
type ChainedCommands = {
    [Item in keyof UnionCommands]: UnionCommands<ChainedCommands>[Item];
} & {
    run: () => boolean;
};
type CanCommands = SingleCommands & {
    chain: () => ChainedCommands;
};
type FocusPosition = 'start' | 'end' | 'all' | number | boolean | null;
type Range = {
    from: number;
    to: number;
};
type NodeRange = {
    node: Node$1;
    from: number;
    to: number;
};
type MarkRange = {
    mark: Mark$1;
    from: number;
    to: number;
};
type Predicate = (node: Node$1) => boolean;
type NodeWithPos = {
    node: Node$1;
    pos: number;
};
type TextSerializer = (props: {
    node: Node$1;
    pos: number;
    parent: Node$1;
    index: number;
    range: Range;
}) => string;
type ExtendedRegExpMatchArray = RegExpMatchArray & {
    data?: Record<string, any>;
};
type Dispatch = ((args?: any) => any) | undefined;

/**
 * Create a new Prosemirror document node from content.
 * @param content The JSON or HTML content to create the document from
 * @param schema The Prosemirror schema to use for the document
 * @param parseOptions Options for the parser
 * @returns The created Prosemirror document node
 */
declare function createDocument(content: Content | Node$1 | Fragment$1, schema: Schema, parseOptions?: ParseOptions, options?: {
    errorOnInvalidContent?: boolean;
}): Node$1;

type CreateNodeFromContentOptions = {
    slice?: boolean;
    parseOptions?: ParseOptions;
    errorOnInvalidContent?: boolean;
};
/**
 * Takes a JSON or HTML content and creates a Prosemirror node or fragment from it.
 * @param content The JSON or HTML content to create the node from
 * @param schema The Prosemirror schema to use for the node
 * @param options Options for the parser
 * @returns The created Prosemirror node or fragment
 */
declare function createNodeFromContent(content: Content | Node$1 | Fragment$1, schema: Schema, options?: CreateNodeFromContentOptions): Node$1 | Fragment$1;

/**
 * Gets the default block type at a given match
 * @param match The content match to get the default block type from
 * @returns The default block type or null
 */
declare function defaultBlockAt(match: ContentMatch): NodeType$1 | null;

/**
 * Find children inside a Prosemirror node that match a predicate.
 * @param node The Prosemirror node to search in
 * @param predicate The predicate to match
 * @returns An array of nodes with their positions
 */
declare function findChildren(node: Node$1, predicate: Predicate): NodeWithPos[];

/**
 * Same as `findChildren` but searches only within a `range`.
 * @param node The Prosemirror node to search in
 * @param range The range to search in
 * @param predicate The predicate to match
 * @returns An array of nodes with their positions
 */
declare function findChildrenInRange(node: Node$1, range: Range, predicate: Predicate): NodeWithPos[];

/**
 * Finds the closest parent node to a resolved position that matches a predicate.
 * @param $pos The resolved position to search from
 * @param predicate The predicate to match
 * @returns The closest parent node to the resolved position that matches the predicate
 * @example ```js
 * findParentNodeClosestToPos($from, node => node.type.name === 'paragraph')
 * ```
 */
declare function findParentNodeClosestToPos($pos: ResolvedPos, predicate: Predicate): {
    pos: number;
    start: number;
    depth: number;
    node: Node$1;
} | undefined;

/**
 * Finds the closest parent node to the current selection that matches a predicate.
 * @param predicate The predicate to match
 * @returns A command that finds the closest parent node to the current selection that matches the predicate
 * @example ```js
 * findParentNode(node => node.type.name === 'paragraph')
 * ```
 */
declare function findParentNode(predicate: Predicate): (selection: Selection) => ReturnType<typeof findParentNodeClosestToPos>;

/**
 * Create a flattened array of extensions by traversing the `addExtensions` field.
 * @param extensions An array of Tiptap extensions
 * @returns A flattened array of Tiptap extensions
 */
declare function flattenExtensions(extensions: Extensions): Extensions;

/**
 * Generate HTML from a JSONContent
 * @param doc The JSONContent to generate HTML from
 * @param extensions The extensions to use for the schema
 * @returns The generated HTML
 */
declare function generateHTML(doc: JSONContent, extensions: Extensions): string;

/**
 * Generate JSONContent from HTML
 * @param html The HTML to generate JSONContent from
 * @param extensions The extensions to use for the schema
 * @returns The generated JSONContent
 */
declare function generateJSON(html: string, extensions: Extensions): Record<string, any>;

/**
 * Generate raw text from a JSONContent
 * @param doc The JSONContent to generate text from
 * @param extensions The extensions to use for the schema
 * @param options Options for the text generation f.e. blockSeparator or textSerializers
 * @returns The generated text
 */
declare function generateText(doc: JSONContent, extensions: Extensions, options?: {
    blockSeparator?: string;
    textSerializers?: Record<string, TextSerializer>;
}): string;

/**
 * Get node or mark attributes by type or name on the current editor state
 * @param state The current editor state
 * @param typeOrName The node or mark type or name
 * @returns The attributes of the node or mark or an empty object
 */
declare function getAttributes(state: EditorState, typeOrName: string | NodeType$1 | MarkType$1): Record<string, any>;

/**
 * Get a list of all extension attributes defined in `addAttribute` and `addGlobalAttribute`.
 * @param extensions List of extensions
 */
declare function getAttributesFromExtensions(extensions: Extensions): ExtensionAttribute[];

type ChangedRange = {
    oldRange: Range;
    newRange: Range;
};
/**
 * Returns a list of changed ranges
 * based on the first and last state of all steps.
 */
declare function getChangedRanges(transform: Transform): ChangedRange[];

interface DebugJSONContent extends JSONContent {
    from: number;
    to: number;
}
declare function getDebugJSON(node: Node$1, startOffset?: number): DebugJSONContent;

interface ExtensionConfig<Options = any, Storage = any> extends ExtendableConfig<Options, Storage, ExtensionConfig<Options, Storage>, null> {
}
/**
 * The Extension class is the base class for all extensions.
 * @see https://tiptap.dev/api/extensions#create-a-new-extension
 */
declare class Extension<Options = any, Storage = any> extends Extendable<Options, Storage, ExtensionConfig<Options, Storage>> {
    type: string;
    /**
     * Create a new Extension instance
     * @param config - Extension configuration object or a function that returns a configuration object
     */
    static create<O = any, S = any>(config?: Partial<ExtensionConfig<O, S>> | (() => Partial<ExtensionConfig<O, S>>)): Extension<O, S>;
    configure(options?: Partial<Options>): Extension<Options, Storage>;
    extend<ExtendedOptions = Options, ExtendedStorage = Storage, ExtendedConfig = ExtensionConfig<ExtendedOptions, ExtendedStorage>>(extendedConfig?: (() => Partial<ExtendedConfig>) | (Partial<ExtendedConfig> & ThisType<{
        name: string;
        options: ExtendedOptions;
        storage: ExtendedStorage;
        editor: Editor;
        type: null;
    }>)): Extension<ExtendedOptions, ExtendedStorage>;
}

/**
 * Returns a field from an extension
 * @param extension The Tiptap extension
 * @param field The field, for example `renderHTML` or `priority`
 * @param context The context object that should be passed as `this` into the function
 * @returns The field value
 */
declare function getExtensionField<T = any, E extends AnyExtension = any>(extension: E, field: keyof ExtensionConfig | keyof MarkConfig | keyof NodeConfig, context?: Omit<MaybeThisParameterType<T>, 'parent'>): RemoveThis<T>;

declare function getHTMLFromFragment(fragment: Fragment$1, schema: Schema): string;

declare function getMarkAttributes(state: EditorState, typeOrName: string | MarkType$1): Record<string, any>;

/**
 * Get the range of a mark at a resolved position.
 */
declare function getMarkRange(
/**
 * The position to get the mark range for.
 */
$pos: ResolvedPos, 
/**
 * The mark type to get the range for.
 */
type: MarkType$1, 
/**
 * The attributes to match against.
 * If not provided, only the first mark at the position will be matched.
 */
attributes?: Record<string, any>): Range | void;

declare function getMarksBetween(from: number, to: number, doc: Node$1): MarkRange[];

declare function getMarkType(nameOrType: string | MarkType$1, schema: Schema): MarkType$1;

/**
 * Finds the first node of a given type or name in the current selection.
 * @param state The editor state.
 * @param typeOrName The node type or name.
 * @param pos The position to start searching from.
 * @param maxDepth The maximum depth to search.
 * @returns The node and the depth as an array.
 */
declare const getNodeAtPosition: (state: EditorState, typeOrName: string | NodeType$1, pos: number, maxDepth?: number) => [Node$1 | null, number];

declare function getNodeAttributes(state: EditorState, typeOrName: string | NodeType$1): Record<string, any>;

declare function getNodeType(nameOrType: string | NodeType$1, schema: Schema): NodeType$1;

declare function getRenderedAttributes(nodeOrMark: Node$1 | Mark$1, extensionAttributes: ExtensionAttribute[]): Record<string, any>;

declare function getSchema(extensions: Extensions, editor?: Editor): Schema;

/**
 * Creates a new Prosemirror schema based on the given extensions.
 * @param extensions An array of Tiptap extensions
 * @param editor The editor instance
 * @returns A Prosemirror schema
 */
declare function getSchemaByResolvedExtensions(extensions: Extensions, editor?: Editor): Schema;

/**
 * Tries to get a node or mark type by its name.
 * @param name The name of the node or mark type
 * @param schema The Prosemiror schema to search in
 * @returns The node or mark type, or null if it doesn't exist
 */
declare function getSchemaTypeByName(name: string, schema: Schema): NodeType$1 | MarkType$1 | null;

/**
 * Get the type of a schema item by its name.
 * @param name The name of the schema item
 * @param schema The Prosemiror schema to search in
 * @returns The type of the schema item (`node` or `mark`), or null if it doesn't exist
 */
declare function getSchemaTypeNameByName(name: string, schema: Schema): 'node' | 'mark' | null;

/**
 * Return attributes of an extension that should be splitted by keepOnSplit flag
 * @param extensionAttributes Array of extension attributes
 * @param typeName The type of the extension
 * @param attributes The attributes of the extension
 * @returns The splitted attributes
 */
declare function getSplittedAttributes(extensionAttributes: ExtensionAttribute[], typeName: string, attributes: Record<string, any>): Record<string, any>;

/**
 * Gets the text of a Prosemirror node
 * @param node The Prosemirror node
 * @param options Options for the text serializer & block separator
 * @returns The text of the node
 * @example ```js
 * const text = getText(node, { blockSeparator: '\n' })
 * ```
 */
declare function getText(node: Node$1, options?: {
    blockSeparator?: string;
    textSerializers?: Record<string, TextSerializer>;
}): string;

/**
 * Gets the text between two positions in a Prosemirror node
 * and serializes it using the given text serializers and block separator (see getText)
 * @param startNode The Prosemirror node to start from
 * @param range The range of the text to get
 * @param options Options for the text serializer & block separator
 * @returns The text between the two positions
 */
declare function getTextBetween(startNode: Node$1, range: Range, options?: {
    blockSeparator?: string;
    textSerializers?: Record<string, TextSerializer>;
}): string;

/**
 * Returns the text content of a resolved prosemirror position
 * @param $from The resolved position to get the text content from
 * @param maxMatch The maximum number of characters to match
 * @returns The text content
 */
declare const getTextContentFromNodes: ($from: ResolvedPos, maxMatch?: number) => string;

/**
 * Find text serializers `toText` in a Prosemirror schema
 * @param schema The Prosemirror schema to search in
 * @returns A record of text serializers by node name
 */
declare function getTextSerializersFromSchema(schema: Schema): Record<string, TextSerializer>;

/**
 * This function merges extension attributes into parserule attributes (`attrs` or `getAttrs`).
 * Cancels when `getAttrs` returned `false`.
 * @param parseRule ProseMirror ParseRule
 * @param extensionAttributes List of attributes to inject
 */
declare function injectExtensionAttributesToParseRule(parseRule: ParseRule, extensionAttributes: ExtensionAttribute[]): ParseRule;

declare function isActive(state: EditorState, name: string | null, attributes?: Record<string, any>): boolean;

declare const isAtEndOfNode: (state: EditorState, nodeType?: string) => boolean;

declare const isAtStartOfNode: (state: EditorState) => boolean;

declare function isExtensionRulesEnabled(extension: AnyExtension, enabled: EnableRules): boolean;

declare function isList(name: string, extensions: Extensions): boolean;

declare function isMarkActive(state: EditorState, typeOrName: MarkType$1 | string | null, attributes?: Record<string, any>): boolean;

declare function isNodeActive(state: EditorState, typeOrName: NodeType$1 | string | null, attributes?: Record<string, any>): boolean;

/**
 * Returns true if the given prosemirror node is empty.
 */
declare function isNodeEmpty(node: Node$1, { checkChildren, ignoreWhitespace, }?: {
    /**
     * When true (default), it will also check if all children are empty.
     */
    checkChildren?: boolean;
    /**
     * When true, it will ignore whitespace when checking for emptiness.
     */
    ignoreWhitespace?: boolean;
}): boolean;

declare function isNodeSelection(value: unknown): value is NodeSelection;

declare function isTextSelection(value: unknown): value is TextSelection;

declare function posToDOMRect(view: EditorView, from: number, to: number): DOMRect;

/**
 * Returns a flattened and sorted extension list while
 * also checking for duplicated extensions and warns the user.
 * @param extensions An array of Tiptap extensions
 * @returns An flattened and sorted array of Tiptap extensions
 */
declare function resolveExtensions(extensions: Extensions): Extensions;

declare function resolveFocusPosition(doc: Node$1, position?: FocusPosition): Selection | null;

type RewriteUnknownContentOptions = {
    /**
     * If true, unknown nodes will be treated as paragraphs
     * @default true
     */
    fallbackToParagraph?: boolean;
};
/**
 * Rewrite unknown nodes and marks within JSON content
 * Allowing for user within the editor
 */
declare function rewriteUnknownContent(
/**
 * The JSON content to clean of unknown nodes and marks
 */
json: JSONContent, 
/**
 * The schema to use for validation
 */
schema: Schema, 
/**
 * Options for the cleaning process
 */
options?: RewriteUnknownContentOptions): {
    /**
     * The cleaned JSON content
     */
    json: JSONContent | null;
    /**
     * The array of nodes and marks that were rewritten
     */
    rewrittenContent: {
        /**
         * The original JSON content that was rewritten
         */
        original: JSONContent;
        /**
         * The name of the node or mark that was unsupported
         */
        unsupported: string;
    }[];
};

declare function selectionToInsertionEnd(tr: Transaction, startLen: number, bias: number): void;

/**
 * Sort extensions by priority.
 * @param extensions An array of Tiptap extensions
 * @returns A sorted array of Tiptap extensions by priority
 */
declare function sortExtensions(extensions: Extensions): Extensions;

declare function splitExtensions(extensions: Extensions): {
    baseExtensions: Extension<any, any>[];
    nodeExtensions: Node<any, any>[];
    markExtensions: Mark<any, any>[];
};

declare class ExtensionManager {
    editor: Editor;
    schema: Schema;
    extensions: Extensions;
    splittableMarks: string[];
    constructor(extensions: Extensions, editor: Editor);
    static resolve: typeof resolveExtensions;
    static sort: typeof sortExtensions;
    static flatten: typeof flattenExtensions;
    /**
     * Get all commands from the extensions.
     * @returns An object with all commands where the key is the command name and the value is the command function
     */
    get commands(): RawCommands;
    /**
     * Get all registered Prosemirror plugins from the extensions.
     * @returns An array of Prosemirror plugins
     */
    get plugins(): Plugin[];
    /**
     * Get all attributes from the extensions.
     * @returns An array of attributes
     */
    get attributes(): ExtensionAttribute[];
    /**
     * Get all node views from the extensions.
     * @returns An object with all node views where the key is the node name and the value is the node view function
     */
    get nodeViews(): Record<string, NodeViewConstructor>;
    get markViews(): Record<string, MarkViewConstructor>;
    /**
     * Go through all extensions, create extension storages & setup marks
     * & bind editor event listener.
     */
    private setupExtensions;
}

declare class NodePos {
    private resolvedPos;
    private isBlock;
    private editor;
    private get name();
    constructor(pos: ResolvedPos, editor: Editor, isBlock?: boolean, node?: Node$1 | null);
    private currentNode;
    get node(): Node$1;
    get element(): HTMLElement;
    actualDepth: number | null;
    get depth(): number;
    get pos(): number;
    get content(): Fragment$1;
    set content(content: Content);
    get attributes(): {
        [key: string]: any;
    };
    get textContent(): string;
    get size(): number;
    get from(): number;
    get range(): Range;
    get to(): number;
    get parent(): NodePos | null;
    get before(): NodePos | null;
    get after(): NodePos | null;
    get children(): NodePos[];
    get firstChild(): NodePos | null;
    get lastChild(): NodePos | null;
    closest(selector: string, attributes?: {
        [key: string]: any;
    }): NodePos | null;
    querySelector(selector: string, attributes?: {
        [key: string]: any;
    }): NodePos | null;
    querySelectorAll(selector: string, attributes?: {
        [key: string]: any;
    }, firstItemOnly?: boolean): NodePos[];
    setAttribute(attributes: {
        [key: string]: any;
    }): void;
}

type ClipboardTextSerializerOptions = {
    blockSeparator?: string;
};
declare const ClipboardTextSerializer: Extension<ClipboardTextSerializerOptions, any>;

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        blur: {
            /**
             * Removes focus from the editor.
             * @example editor.commands.blur()
             */
            blur: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        clearContent: {
            /**
             * Clear the whole document.
             * @example editor.commands.clearContent()
             */
            clearContent: (
            /**
             * Whether to emit an update event.
             * @default true
             */
            emitUpdate?: boolean) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        clearNodes: {
            /**
             * Normalize nodes to a simple paragraph.
             * @example editor.commands.clearNodes()
             */
            clearNodes: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        command: {
            /**
             * Define a command inline.
             * @param fn The command function.
             * @example
             * editor.commands.command(({ tr, state }) => {
             *   ...
             *   return true
             * })
             */
            command: (fn: (props: Parameters<Command>[0]) => boolean) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        createParagraphNear: {
            /**
             * Create a paragraph nearby.
             * @example editor.commands.createParagraphNear()
             */
            createParagraphNear: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        cut: {
            /**
             * Cuts content from a range and inserts it at a given position.
             * @param range The range to cut.
             * @param range.from The start position of the range.
             * @param range.to The end position of the range.
             * @param targetPos The position to insert the content at.
             * @example editor.commands.cut({ from: 1, to: 3 }, 5)
             */
            cut: ({ from, to }: {
                from: number;
                to: number;
            }, targetPos: number) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        deleteCurrentNode: {
            /**
             * Delete the node that currently has the selection anchor.
             * @example editor.commands.deleteCurrentNode()
             */
            deleteCurrentNode: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        deleteNode: {
            /**
             * Delete a node with a given type or name.
             * @param typeOrName The type or name of the node.
             * @example editor.commands.deleteNode('paragraph')
             */
            deleteNode: (typeOrName: string | NodeType$1) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        deleteRange: {
            /**
             * Delete a given range.
             * @param range The range to delete.
             * @example editor.commands.deleteRange({ from: 1, to: 3 })
             */
            deleteRange: (range: Range) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        deleteSelection: {
            /**
             * Delete the selection, if there is one.
             * @example editor.commands.deleteSelection()
             */
            deleteSelection: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        enter: {
            /**
             * Trigger enter.
             * @example editor.commands.enter()
             */
            enter: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        exitCode: {
            /**
             * Exit from a code block.
             * @example editor.commands.exitCode()
             */
            exitCode: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        extendMarkRange: {
            /**
             * Extends the text selection to the current mark by type or name.
             * @param typeOrName The type or name of the mark.
             * @param attributes The attributes of the mark.
             * @example editor.commands.extendMarkRange('bold')
             * @example editor.commands.extendMarkRange('mention', { userId: "1" })
             */
            extendMarkRange: (
            /**
             * The type or name of the mark.
             */
            typeOrName: string | MarkType$1, 
            /**
             * The attributes of the mark.
             */
            attributes?: Record<string, any>) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        first: {
            /**
             * Runs one command after the other and stops at the first which returns true.
             * @param commands The commands to run.
             * @example editor.commands.first([command1, command2])
             */
            first: (commands: Command[] | ((props: CommandProps) => Command[])) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        focus: {
            /**
             * Focus the editor at the given position.
             * @param position The position to focus at.
             * @param options.scrollIntoView Scroll the focused position into view after focusing
             * @example editor.commands.focus()
             * @example editor.commands.focus(32, { scrollIntoView: false })
             */
            focus: (
            /**
             * The position to focus at.
             */
            position?: FocusPosition, 
            /**
             * Optional options
             * @default { scrollIntoView: true }
             */
            options?: {
                scrollIntoView?: boolean;
            }) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        forEach: {
            /**
             * Loop through an array of items.
             */
            forEach: <T>(items: T[], fn: (item: T, props: CommandProps & {
                index: number;
            }) => boolean) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        insertContent: {
            /**
             * Insert a node or string of HTML at the current position.
             * @example editor.commands.insertContent('<h1>Example</h1>')
             * @example editor.commands.insertContent('<h1>Example</h1>', { updateSelection: false })
             */
            insertContent: (
            /**
             * The ProseMirror content to insert.
             */
            value: Content | Node$1 | Fragment$1, 
            /**
             * Optional options
             */
            options?: {
                /**
                 * Options for parsing the content.
                 */
                parseOptions?: ParseOptions;
                /**
                 * Whether to update the selection after inserting the content.
                 */
                updateSelection?: boolean;
                applyInputRules?: boolean;
                applyPasteRules?: boolean;
            }) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        insertContentAt: {
            /**
             * Insert a node or string of HTML at a specific position.
             * @example editor.commands.insertContentAt(0, '<h1>Example</h1>')
             */
            insertContentAt: (
            /**
             * The position to insert the content at.
             */
            position: number | Range, 
            /**
             * The ProseMirror content to insert.
             */
            value: Content | Node$1 | Fragment$1, 
            /**
             * Optional options
             */
            options?: {
                /**
                 * Options for parsing the content.
                 */
                parseOptions?: ParseOptions;
                /**
                 * Whether to update the selection after inserting the content.
                 */
                updateSelection?: boolean;
                /**
                 * Whether to apply input rules after inserting the content.
                 */
                applyInputRules?: boolean;
                /**
                 * Whether to apply paste rules after inserting the content.
                 */
                applyPasteRules?: boolean;
                /**
                 * Whether to throw an error if the content is invalid.
                 */
                errorOnInvalidContent?: boolean;
            }) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        joinUp: {
            /**
             * Join the selected block or, if there is a text selection, the closest ancestor block of the selection that can be joined, with the sibling above it.
             * @example editor.commands.joinUp()
             */
            joinUp: () => ReturnType;
        };
        joinDown: {
            /**
             * Join the selected block, or the closest ancestor of the selection that can be joined, with the sibling after it.
             * @example editor.commands.joinDown()
             */
            joinDown: () => ReturnType;
        };
        joinBackward: {
            /**
             * If the selection is empty and at the start of a textblock, try to reduce the distance between that block and the one before it—if there's a block directly before it that can be joined, join them.
             * If not, try to move the selected block closer to the next one in the document structure by lifting it out of its
             * parent or moving it into a parent of the previous block. Will use the view for accurate (bidi-aware) start-of-textblock detection if given.
             * @example editor.commands.joinBackward()
             */
            joinBackward: () => ReturnType;
        };
        joinForward: {
            /**
             * If the selection is empty and the cursor is at the end of a textblock, try to reduce or remove the boundary between that block and the one after it,
             * either by joining them or by moving the other block closer to this one in the tree structure.
             * Will use the view for accurate start-of-textblock detection if given.
             * @example editor.commands.joinForward()
             */
            joinForward: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        joinItemBackward: {
            /**
             * Join two items backward.
             * @example editor.commands.joinItemBackward()
             */
            joinItemBackward: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        joinItemForward: {
            /**
             * Join two items Forwards.
             * @example editor.commands.joinItemForward()
             */
            joinItemForward: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        joinTextblockBackward: {
            /**
             * A more limited form of joinBackward that only tries to join the current textblock to the one before it, if the cursor is at the start of a textblock.
             */
            joinTextblockBackward: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        joinTextblockForward: {
            /**
             * A more limited form of joinForward that only tries to join the current textblock to the one after it, if the cursor is at the end of a textblock.
             */
            joinTextblockForward: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        keyboardShortcut: {
            /**
             * Trigger a keyboard shortcut.
             * @param name The name of the keyboard shortcut.
             * @example editor.commands.keyboardShortcut('Mod-b')
             */
            keyboardShortcut: (name: string) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        lift: {
            /**
             * Removes an existing wrap if possible lifting the node out of it
             * @param typeOrName The type or name of the node.
             * @param attributes The attributes of the node.
             * @example editor.commands.lift('paragraph')
             * @example editor.commands.lift('heading', { level: 1 })
             */
            lift: (typeOrName: string | NodeType$1, attributes?: Record<string, any>) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        liftEmptyBlock: {
            /**
             * If the cursor is in an empty textblock that can be lifted, lift the block.
             * @example editor.commands.liftEmptyBlock()
             */
            liftEmptyBlock: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        liftListItem: {
            /**
             * Create a command to lift the list item around the selection up into a wrapping list.
             * @param typeOrName The type or name of the node.
             * @example editor.commands.liftListItem('listItem')
             */
            liftListItem: (typeOrName: string | NodeType$1) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        newlineInCode: {
            /**
             * Add a newline character in code.
             * @example editor.commands.newlineInCode()
             */
            newlineInCode: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        resetAttributes: {
            /**
             * Resets some node attributes to the default value.
             * @param typeOrName The type or name of the node.
             * @param attributes The attributes of the node to reset.
             * @example editor.commands.resetAttributes('heading', 'level')
             */
            resetAttributes: (typeOrName: string | NodeType$1 | MarkType$1, attributes: string | string[]) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        scrollIntoView: {
            /**
             * Scroll the selection into view.
             * @example editor.commands.scrollIntoView()
             */
            scrollIntoView: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        selectAll: {
            /**
             * Select the whole document.
             * @example editor.commands.selectAll()
             */
            selectAll: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        selectNodeBackward: {
            /**
             * Select a node backward.
             * @example editor.commands.selectNodeBackward()
             */
            selectNodeBackward: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        selectNodeForward: {
            /**
             * Select a node forward.
             * @example editor.commands.selectNodeForward()
             */
            selectNodeForward: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        selectParentNode: {
            /**
             * Select the parent node.
             * @example editor.commands.selectParentNode()
             */
            selectParentNode: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        selectTextblockEnd: {
            /**
             * Moves the cursor to the end of current text block.
             * @example editor.commands.selectTextblockEnd()
             */
            selectTextblockEnd: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        selectTextblockStart: {
            /**
             * Moves the cursor to the start of current text block.
             * @example editor.commands.selectTextblockStart()
             */
            selectTextblockStart: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        setContent: {
            /**
             * Replace the whole document with new content.
             * @param content The new content.
             * @param emitUpdate Whether to emit an update event.
             * @param parseOptions Options for parsing the content.
             * @example editor.commands.setContent('<p>Example text</p>')
             */
            setContent: (
            /**
             * The new content.
             */
            content: Content | Fragment$1 | Node$1, 
            /**
             * Options for `setContent`.
             */
            options?: {
                /**
                 * Options for parsing the content.
                 * @default {}
                 */
                parseOptions?: ParseOptions;
                /**
                 * Whether to throw an error if the content is invalid.
                 */
                errorOnInvalidContent?: boolean;
                /**
                 * Whether to emit an update event.
                 * @default true
                 */
                emitUpdate?: boolean;
            }) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        setMark: {
            /**
             * Add a mark with new attributes.
             * @param typeOrName The mark type or name.
             * @example editor.commands.setMark('bold', { level: 1 })
             */
            setMark: (typeOrName: string | MarkType$1, attributes?: Record<string, any>) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        setMeta: {
            /**
             * Store a metadata property in the current transaction.
             * @param key The key of the metadata property.
             * @param value The value to store.
             * @example editor.commands.setMeta('foo', 'bar')
             */
            setMeta: (key: string | Plugin | PluginKey, value: any) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        setNode: {
            /**
             * Replace a given range with a node.
             * @param typeOrName The type or name of the node
             * @param attributes The attributes of the node
             * @example editor.commands.setNode('paragraph')
             */
            setNode: (typeOrName: string | NodeType$1, attributes?: Record<string, any>) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        setNodeSelection: {
            /**
             * Creates a NodeSelection.
             * @param position - Position of the node.
             * @example editor.commands.setNodeSelection(10)
             */
            setNodeSelection: (position: number) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        setTextSelection: {
            /**
             * Creates a TextSelection.
             * @param position The position of the selection.
             * @example editor.commands.setTextSelection(10)
             */
            setTextSelection: (position: number | Range) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        sinkListItem: {
            /**
             * Sink the list item down into an inner list.
             * @param typeOrName The type or name of the node.
             * @example editor.commands.sinkListItem('listItem')
             */
            sinkListItem: (typeOrName: string | NodeType$1) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        splitBlock: {
            /**
             * Forks a new node from an existing node.
             * @param options.keepMarks Keep marks from the previous node.
             * @example editor.commands.splitBlock()
             * @example editor.commands.splitBlock({ keepMarks: true })
             */
            splitBlock: (options?: {
                keepMarks?: boolean;
            }) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        splitListItem: {
            /**
             * Splits one list item into two list items.
             * @param typeOrName The type or name of the node.
             * @param overrideAttrs The attributes to ensure on the new node.
             * @example editor.commands.splitListItem('listItem')
             */
            splitListItem: (typeOrName: string | NodeType$1, overrideAttrs?: Record<string, any>) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        toggleList: {
            /**
             * Toggle between different list types.
             * @param listTypeOrName The type or name of the list.
             * @param itemTypeOrName The type or name of the list item.
             * @param keepMarks Keep marks when toggling.
             * @param attributes Attributes for the new list.
             * @example editor.commands.toggleList('bulletList', 'listItem')
             */
            toggleList: (listTypeOrName: string | NodeType$1, itemTypeOrName: string | NodeType$1, keepMarks?: boolean, attributes?: Record<string, any>) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        toggleMark: {
            /**
             * Toggle a mark on and off.
             * @param typeOrName The mark type or name.
             * @param attributes The attributes of the mark.
             * @param options.extendEmptyMarkRange Removes the mark even across the current selection. Defaults to `false`.
             * @example editor.commands.toggleMark('bold')
             */
            toggleMark: (
            /**
             * The mark type or name.
             */
            typeOrName: string | MarkType$1, 
            /**
             * The attributes of the mark.
             */
            attributes?: Record<string, any>, options?: {
                /**
                 * Removes the mark even across the current selection. Defaults to `false`.
                 */
                extendEmptyMarkRange?: boolean;
            }) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        toggleNode: {
            /**
             * Toggle a node with another node.
             * @param typeOrName The type or name of the node.
             * @param toggleTypeOrName The type or name of the node to toggle.
             * @param attributes The attributes of the node.
             * @example editor.commands.toggleNode('heading', 'paragraph')
             */
            toggleNode: (typeOrName: string | NodeType$1, toggleTypeOrName: string | NodeType$1, attributes?: Record<string, any>) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        toggleWrap: {
            /**
             * Wraps nodes in another node, or removes an existing wrap.
             * @param typeOrName The type or name of the node.
             * @param attributes The attributes of the node.
             * @example editor.commands.toggleWrap('blockquote')
             */
            toggleWrap: (typeOrName: string | NodeType$1, attributes?: Record<string, any>) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        undoInputRule: {
            /**
             * Undo an input rule.
             * @example editor.commands.undoInputRule()
             */
            undoInputRule: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        unsetAllMarks: {
            /**
             * Remove all marks in the current selection.
             * @example editor.commands.unsetAllMarks()
             */
            unsetAllMarks: () => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        unsetMark: {
            /**
             * Remove all marks in the current selection.
             * @param typeOrName The mark type or name.
             * @param options.extendEmptyMarkRange Removes the mark even across the current selection. Defaults to `false`.
             * @example editor.commands.unsetMark('bold')
             */
            unsetMark: (
            /**
             * The mark type or name.
             */
            typeOrName: string | MarkType$1, options?: {
                /**
                 * Removes the mark even across the current selection. Defaults to `false`.
                 */
                extendEmptyMarkRange?: boolean;
            }) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        updateAttributes: {
            /**
             * Update attributes of a node or mark.
             * @param typeOrName The type or name of the node or mark.
             * @param attributes The attributes of the node or mark.
             * @example editor.commands.updateAttributes('mention', { userId: "2" })
             */
            updateAttributes: (
            /**
             * The type or name of the node or mark.
             */
            typeOrName: string | NodeType$1 | MarkType$1, 
            /**
             * The attributes of the node or mark.
             */
            attributes: Record<string, any>) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        wrapIn: {
            /**
             * Wraps nodes in another node.
             * @param typeOrName The type or name of the node.
             * @param attributes The attributes of the node.
             * @example editor.commands.wrapIn('blockquote')
             */
            wrapIn: (typeOrName: string | NodeType$1, attributes?: Record<string, any>) => ReturnType;
        };
    }
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        wrapInList: {
            /**
             * Wrap a node in a list.
             * @param typeOrName The type or name of the node.
             * @param attributes The attributes of the node.
             * @example editor.commands.wrapInList('bulletList')
             */
            wrapInList: (typeOrName: string | NodeType$1, attributes?: Record<string, any>) => ReturnType;
        };
    }
}

declare const Commands$1: Extension<any, any>;

/**
 * This extension allows you to be notified when the user deletes content you are interested in.
 */
declare const Delete: Extension<any, any>;

declare const Drop: Extension<any, any>;

declare const Editable: Extension<any, any>;

declare const focusEventsPluginKey: PluginKey<any>;
declare const FocusEvents: Extension<any, any>;

declare const Keymap: Extension<any, any>;

declare const Paste: Extension<any, any>;

declare const Tabindex: Extension<any, any>;

declare const index_ClipboardTextSerializer: typeof ClipboardTextSerializer;
declare const index_Delete: typeof Delete;
declare const index_Drop: typeof Drop;
declare const index_Editable: typeof Editable;
declare const index_FocusEvents: typeof FocusEvents;
declare const index_Keymap: typeof Keymap;
declare const index_Paste: typeof Paste;
declare const index_Tabindex: typeof Tabindex;
declare const index_focusEventsPluginKey: typeof focusEventsPluginKey;
declare namespace index {
  export { index_ClipboardTextSerializer as ClipboardTextSerializer, Commands$1 as Commands, index_Delete as Delete, index_Drop as Drop, index_Editable as Editable, index_FocusEvents as FocusEvents, index_Keymap as Keymap, index_Paste as Paste, index_Tabindex as Tabindex, index_focusEventsPluginKey as focusEventsPluginKey };
}

interface TiptapEditorHTMLElement extends HTMLElement {
    editor?: Editor;
}
declare class Editor extends EventEmitter<EditorEvents> {
    private commandManager;
    extensionManager: ExtensionManager;
    private css;
    schema: Schema;
    private editorView;
    isFocused: boolean;
    private editorState;
    /**
     * The editor is considered initialized after the `create` event has been emitted.
     */
    isInitialized: boolean;
    extensionStorage: Storage;
    /**
     * A unique ID for this editor instance.
     */
    instanceId: string;
    options: EditorOptions;
    constructor(options?: Partial<EditorOptions>);
    /**
     * Attach the editor to the DOM, creating a new editor view.
     */
    mount(el: NonNullable<EditorOptions['element']> & {}): void;
    /**
     * Remove the editor from the DOM, but still allow remounting at a different point in time
     */
    unmount(): void;
    /**
     * Returns the editor storage.
     */
    get storage(): Storage;
    /**
     * An object of all registered commands.
     */
    get commands(): SingleCommands;
    /**
     * Create a command chain to call multiple commands at once.
     */
    chain(): ChainedCommands;
    /**
     * Check if a command or a command chain can be executed. Without executing it.
     */
    can(): CanCommands;
    /**
     * Inject CSS styles.
     */
    private injectCSS;
    /**
     * Update editor options.
     *
     * @param options A list of options
     */
    setOptions(options?: Partial<EditorOptions>): void;
    /**
     * Update editable state of the editor.
     */
    setEditable(editable: boolean, emitUpdate?: boolean): void;
    /**
     * Returns whether the editor is editable.
     */
    get isEditable(): boolean;
    /**
     * Returns the editor state.
     */
    get view(): EditorView;
    /**
     * Returns the editor state.
     */
    get state(): EditorState;
    /**
     * Register a ProseMirror plugin.
     *
     * @param plugin A ProseMirror plugin
     * @param handlePlugins Control how to merge the plugin into the existing plugins.
     * @returns The new editor state
     */
    registerPlugin(plugin: Plugin, handlePlugins?: (newPlugin: Plugin, plugins: Plugin[]) => Plugin[]): EditorState;
    /**
     * Unregister a ProseMirror plugin.
     *
     * @param nameOrPluginKeyToRemove The plugins name
     * @returns The new editor state or undefined if the editor is destroyed
     */
    unregisterPlugin(nameOrPluginKeyToRemove: string | PluginKey | (string | PluginKey)[]): EditorState | undefined;
    /**
     * Creates an extension manager.
     */
    private createExtensionManager;
    /**
     * Creates an command manager.
     */
    private createCommandManager;
    /**
     * Creates a ProseMirror schema.
     */
    private createSchema;
    /**
     * Creates the initial document.
     */
    private createDoc;
    /**
     * Creates a ProseMirror view.
     */
    private createView;
    /**
     * Creates all node and mark views.
     */
    createNodeViews(): void;
    /**
     * Prepend class name to element.
     */
    prependClass(): void;
    isCapturingTransaction: boolean;
    private capturedTransaction;
    captureTransaction(fn: () => void): Transaction | null;
    /**
     * The callback over which to send transactions (state updates) produced by the view.
     *
     * @param transaction An editor state transaction
     */
    private dispatchTransaction;
    /**
     * Get attributes of the currently selected node or mark.
     */
    getAttributes(nameOrType: string | NodeType$1 | MarkType$1): Record<string, any>;
    /**
     * Returns if the currently selected node or mark is active.
     *
     * @param name Name of the node or mark
     * @param attributes Attributes of the node or mark
     */
    isActive(name: string, attributes?: {}): boolean;
    isActive(attributes: {}): boolean;
    /**
     * Get the document as JSON.
     */
    getJSON(): DocumentType<Record<string, any> | undefined, NodeType<string, undefined | Record<string, any>, any, (NodeType | TextType)[]>[]>;
    /**
     * Get the document as HTML.
     */
    getHTML(): string;
    /**
     * Get the document as text.
     */
    getText(options?: {
        blockSeparator?: string;
        textSerializers?: Record<string, TextSerializer>;
    }): string;
    /**
     * Check if there is no content.
     */
    get isEmpty(): boolean;
    /**
     * Destroy the editor.
     */
    destroy(): void;
    /**
     * Check if the editor is already destroyed.
     */
    get isDestroyed(): boolean;
    $node(selector: string, attributes?: {
        [key: string]: any;
    }): NodePos | null;
    $nodes(selector: string, attributes?: {
        [key: string]: any;
    }): NodePos[] | null;
    $pos(pos: number): NodePos;
    get $doc(): NodePos;
}

declare class CommandManager {
    editor: Editor;
    rawCommands: AnyCommands;
    customState?: EditorState;
    constructor(props: {
        editor: Editor;
        state?: EditorState;
    });
    get hasCustomState(): boolean;
    get state(): EditorState;
    get commands(): SingleCommands;
    get chain(): () => ChainedCommands;
    get can(): () => CanCommands;
    createChain(startTr?: Transaction, shouldDispatch?: boolean): ChainedCommands;
    createCan(startTr?: Transaction): CanCommands;
    buildProps(tr: Transaction, shouldDispatch?: boolean): CommandProps;
}

/**
 * Build an input rule that adds a mark when the
 * matched text is typed into it.
 * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules
 */
declare function markInputRule(config: {
    find: InputRuleFinder;
    type: MarkType$1;
    getAttributes?: Record<string, any> | ((match: ExtendedRegExpMatchArray) => Record<string, any>) | false | null;
}): InputRule;

/**
 * Build an input rule that adds a node when the
 * matched text is typed into it.
 * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules
 */
declare function nodeInputRule(config: {
    /**
     * The regex to match.
     */
    find: InputRuleFinder;
    /**
     * The node type to add.
     */
    type: NodeType$1;
    /**
     * A function that returns the attributes for the node
     * can also be an object of attributes
     */
    getAttributes?: Record<string, any> | ((match: ExtendedRegExpMatchArray) => Record<string, any>) | false | null;
}): InputRule;

/**
 * Build an input rule that changes the type of a textblock when the
 * matched text is typed into it. When using a regular expresion you’ll
 * probably want the regexp to start with `^`, so that the pattern can
 * only occur at the start of a textblock.
 * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules
 */
declare function textblockTypeInputRule(config: {
    find: InputRuleFinder;
    type: NodeType$1;
    getAttributes?: Record<string, any> | ((match: ExtendedRegExpMatchArray) => Record<string, any>) | false | null;
}): InputRule;

/**
 * Build an input rule that replaces text when the
 * matched text is typed into it.
 * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules
 */
declare function textInputRule(config: {
    find: InputRuleFinder;
    replace: string;
}): InputRule;

/**
 * Build an input rule for automatically wrapping a textblock when a
 * given string is typed. When using a regular expresion you’ll
 * probably want the regexp to start with `^`, so that the pattern can
 * only occur at the start of a textblock.
 *
 * `type` is the type of node to wrap in.
 *
 * By default, if there’s a node with the same type above the newly
 * wrapped node, the rule will try to join those
 * two nodes. You can pass a join predicate, which takes a regular
 * expression match and the node before the wrapped node, and can
 * return a boolean to indicate whether a join should happen.
 * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules
 */
declare function wrappingInputRule(config: {
    find: InputRuleFinder;
    type: NodeType$1;
    keepMarks?: boolean;
    keepAttributes?: boolean;
    editor?: Editor;
    getAttributes?: Record<string, any> | ((match: ExtendedRegExpMatchArray) => Record<string, any>) | false | null;
    joinPredicate?: (match: ExtendedRegExpMatchArray, node: Node$1) => boolean;
}): InputRule;

type Attributes = Record<string, any>;
type DOMOutputSpecElement = 0 | Attributes | DOMOutputSpecArray;
/**
 * Better describes the output of a `renderHTML` function in prosemirror
 * @see https://prosemirror.net/docs/ref/#model.DOMOutputSpec
 */
type DOMOutputSpecArray = [string] | [string, Attributes] | [string, 0] | [string, Attributes, 0] | [string, Attributes, DOMOutputSpecArray | 0] | [string, DOMOutputSpecArray];
type JSXRenderer = (tag: 'slot' | string | ((props?: Attributes) => DOMOutputSpecArray | DOMOutputSpecElement), props?: Attributes, ...children: JSXRenderer[]) => DOMOutputSpecArray | DOMOutputSpecElement;
declare function Fragment(props: {
    children: JSXRenderer[];
}): JSXRenderer[];
declare const h: JSXRenderer;

declare function updateMarkViewAttributes(checkMark: Mark$1, editor: Editor, attrs?: Record<string, any>): void;
declare class MarkView<Component, Options extends MarkViewRendererOptions = MarkViewRendererOptions> {
    component: Component;
    editor: Editor;
    options: Options;
    mark: MarkViewProps['mark'];
    HTMLAttributes: MarkViewProps['HTMLAttributes'];
    constructor(component: Component, props: MarkViewProps, options?: Partial<Options>);
    get dom(): HTMLElement;
    get contentDOM(): HTMLElement | null;
    /**
     * Update the attributes of the mark in the document.
     * @param attrs The attributes to update.
     */
    updateAttributes(attrs: Record<string, any>, checkMark?: Mark$1): void;
    ignoreMutation(mutation: ViewMutationRecord): boolean;
}

/**
 * Node views are used to customize the rendered DOM structure of a node.
 * @see https://tiptap.dev/guide/node-views
 */
declare class NodeView<Component, NodeEditor extends Editor = Editor, Options extends NodeViewRendererOptions = NodeViewRendererOptions> implements NodeView$1 {
    component: Component;
    editor: NodeEditor;
    options: Options;
    extension: NodeViewRendererProps['extension'];
    node: NodeViewRendererProps['node'];
    decorations: NodeViewRendererProps['decorations'];
    innerDecorations: NodeViewRendererProps['innerDecorations'];
    view: NodeViewRendererProps['view'];
    getPos: NodeViewRendererProps['getPos'];
    HTMLAttributes: NodeViewRendererProps['HTMLAttributes'];
    isDragging: boolean;
    constructor(component: Component, props: NodeViewRendererProps, options?: Partial<Options>);
    mount(): void;
    get dom(): HTMLElement;
    get contentDOM(): HTMLElement | null;
    onDragStart(event: DragEvent): void;
    stopEvent(event: Event): boolean;
    /**
     * Called when a DOM [mutation](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver) or a selection change happens within the view.
     * @return `false` if the editor should re-read the selection or re-parse the range around the mutation
     * @return `true` if it can safely be ignored.
     */
    ignoreMutation(mutation: ViewMutationRecord): boolean;
    /**
     * Update the attributes of the prosemirror node.
     */
    updateAttributes(attributes: Record<string, any>): void;
    /**
     * Delete the node.
     */
    deleteNode(): void;
}

/**
 * Build an paste rule that adds a mark when the
 * matched text is pasted into it.
 * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules
 */
declare function markPasteRule(config: {
    find: PasteRuleFinder;
    type: MarkType$1;
    getAttributes?: Record<string, any> | ((match: ExtendedRegExpMatchArray, event: ClipboardEvent) => Record<string, any>) | false | null;
}): PasteRule;

/**
 * Build an paste rule that adds a node when the
 * matched text is pasted into it.
 * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules
 */
declare function nodePasteRule(config: {
    find: PasteRuleFinder;
    type: NodeType$1;
    getAttributes?: Record<string, any> | ((match: ExtendedRegExpMatchArray, event: ClipboardEvent) => Record<string, any>) | false | null;
    getContent?: JSONContent[] | ((attrs: Record<string, any>) => JSONContent[]) | false | null;
}): PasteRule;

/**
 * Build an paste rule that replaces text when the
 * matched text is pasted into it.
 * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules
 */
declare function textPasteRule(config: {
    find: PasteRuleFinder;
    replace: string;
}): PasteRule;

interface TrackerResult {
    position: number;
    deleted: boolean;
}
declare class Tracker {
    transaction: Transaction;
    currentStep: number;
    constructor(transaction: Transaction);
    map(position: number): TrackerResult;
}

/**
 * Optionally calls `value` as a function.
 * Otherwise it is returned directly.
 * @param value Function or any value.
 * @param context Optional context to bind to function.
 * @param props Optional props to pass to function.
 */
declare function callOrReturn<T>(value: T, context?: any, ...props: any[]): MaybeReturnType<T>;

declare function canInsertNode(state: EditorState, nodeType: NodeType$1): boolean;

declare function createStyleTag(style: string, nonce?: string, suffix?: string): HTMLStyleElement;

/**
 * Remove a property or an array of properties from an object
 * @param obj Object
 * @param key Key to remove
 */
declare function deleteProps(obj: Record<string, any>, propOrProps: string | string[]): Record<string, any>;

declare function elementFromString(value: string): HTMLElement;

declare function escapeForRegEx(string: string): string;

/**
 * Find duplicates in an array.
 */
declare function findDuplicates<T>(items: T[]): T[];

declare function fromString(value: any): any;

declare function isAndroid(): boolean;

declare function isEmptyObject(value?: {}): boolean;

declare function isFunction(value: any): value is Function;

declare function isiOS(): boolean;

declare function isMacOS(): boolean;

declare function isNumber(value: any): value is number;

declare function isPlainObject(value: any): value is Record<string, any>;

declare function isRegExp(value: any): value is RegExp;

declare function isString(value: any): value is string;

declare function mergeAttributes(...objects: Record<string, any>[]): Record<string, any>;

declare function mergeDeep(target: Record<string, any>, source: Record<string, any>): Record<string, any>;

declare function minMax(value?: number, min?: number, max?: number): number;

/**
 * Check if object1 includes object2
 * @param object1 Object
 * @param object2 Object
 */
declare function objectIncludes(object1: Record<string, any>, object2: Record<string, any>, options?: {
    strict: boolean;
}): boolean;

/**
 * Removes duplicated values within an array.
 * Supports numbers, strings and objects.
 */
declare function removeDuplicates<T>(array: T[], by?: {
    (value: any, replacer?: (this: any, key: string, value: any) => any, space?: string | number): string;
    (value: any, replacer?: (number | string)[] | null, space?: string | number): string;
}): T[];

interface Commands<ReturnType = any> {
}
interface Storage {
}

export { type AnyCommands, type AnyConfig, type AnyExtension, type Attribute, type Attributes$1 as Attributes, type CanCommands, type ChainedCommands, type ChangedRange, type Command, CommandManager, type CommandProps, type CommandSpec, type Commands, type Content, type CreateNodeFromContentOptions, type DOMNode, type DOMOutputSpecArray$1 as DOMOutputSpecArray, type DecorationType, type DecorationWithType, type Diff, type Dispatch, type DocumentType, Editor, type EditorEvents, type EditorOptions, type EnableRules, type ExtendedRegExpMatchArray, Extension, type ExtensionAttribute, type ExtensionConfig, type Extensions, type FocusPosition, Fragment, type GlobalAttributes, type HTMLContent, InputRule, type InputRuleFinder, type InputRuleMatch, type JSONContent, type KeyboardShortcutCommand, type KeysWithTypeOf, Mark, type MarkConfig, type MarkRange, type MarkType, MarkView, type MarkViewProps, type MarkViewRenderer, type MarkViewRendererOptions, type MarkViewRendererProps, type MaybeReturnType, type MaybeThisParameterType, Node, type NodeConfig, NodePos, type NodeRange, type NodeType, NodeView, type NodeViewProps, type NodeViewRenderer, type NodeViewRendererOptions, type NodeViewRendererProps, type NodeWithPos, type Overwrite, type ParentConfig, PasteRule, type PasteRuleFinder, type PasteRuleMatch, type PickValue, type Predicate, type Primitive, type Range, type RawCommands, type RemoveThis, type SingleCommands, type Storage, type TextSerializer, type TextType, type TiptapEditorHTMLElement, Tracker, type TrackerResult, type UnionCommands, type UnionToIntersection, type ValuesOf, callOrReturn, canInsertNode, combineTransactionSteps, createChainableState, createDocument, h as createElement, createNodeFromContent, createStyleTag, defaultBlockAt, deleteProps, elementFromString, escapeForRegEx, index as extensions, findChildren, findChildrenInRange, findDuplicates, findParentNode, findParentNodeClosestToPos, flattenExtensions, fromString, generateHTML, generateJSON, generateText, getAttributes, getAttributesFromExtensions, getChangedRanges, getDebugJSON, getExtensionField, getHTMLFromFragment, getMarkAttributes, getMarkRange, getMarkType, getMarksBetween, getNodeAtPosition, getNodeAttributes, getNodeType, getRenderedAttributes, getSchema, getSchemaByResolvedExtensions, getSchemaTypeByName, getSchemaTypeNameByName, getSplittedAttributes, getText, getTextBetween, getTextContentFromNodes, getTextSerializersFromSchema, h, injectExtensionAttributesToParseRule, inputRulesPlugin, isActive, isAndroid, isAtEndOfNode, isAtStartOfNode, isEmptyObject, isExtensionRulesEnabled, isFunction, isList, isMacOS, isMarkActive, isNodeActive, isNodeEmpty, isNodeSelection, isNumber, isPlainObject, isRegExp, isString, isTextSelection, isiOS, markInputRule, markPasteRule, mergeAttributes, mergeDeep, minMax, nodeInputRule, nodePasteRule, objectIncludes, pasteRulesPlugin, posToDOMRect, removeDuplicates, resolveExtensions, resolveFocusPosition, rewriteUnknownContent, selectionToInsertionEnd, sortExtensions, splitExtensions, textInputRule, textPasteRule, textblockTypeInputRule, updateMarkViewAttributes, wrappingInputRule };
