{"version": 3, "sources": ["../src/index.ts", "../src/underline.ts"], "sourcesContent": ["import { Underline } from './underline.js'\n\nexport * from './underline.js'\n\nexport default Underline\n", "import { Mark, mergeAttributes } from '@tiptap/core'\n\nexport interface UnderlineOptions {\n  /**\n   * HTML attributes to add to the underline element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    underline: {\n      /**\n       * Set an underline mark\n       * @example editor.commands.setUnderline()\n       */\n      setUnderline: () => ReturnType\n      /**\n       * Toggle an underline mark\n       * @example editor.commands.toggleUnderline()\n       */\n      toggleUnderline: () => ReturnType\n      /**\n       * Unset an underline mark\n       * @example editor.commands.unsetUnderline()\n       */\n      unsetUnderline: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to create underline text.\n * @see https://www.tiptap.dev/api/marks/underline\n */\nexport const Underline = Mark.create<UnderlineOptions>({\n  name: 'underline',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'u',\n      },\n      {\n        style: 'text-decoration',\n        consuming: false,\n        getAttrs: style => ((style as string).includes('underline') ? {} : false),\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['u', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setUnderline:\n        () =>\n        ({ commands }) => {\n          return commands.setMark(this.name)\n        },\n      toggleUnderline:\n        () =>\n        ({ commands }) => {\n          return commands.toggleMark(this.name)\n        },\n      unsetUnderline:\n        () =>\n        ({ commands }) => {\n          return commands.unsetMark(this.name)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-u': () => this.editor.commands.toggleUnderline(),\n      'Mod-U': () => this.editor.commands.toggleUnderline(),\n    }\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,kBAAsC;AAqC/B,IAAM,YAAY,iBAAK,OAAyB;AAAA,EACrD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,YAAY;AACV,WAAO;AAAA,MACL;AAAA,QACE,KAAK;AAAA,MACP;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,WAAW;AAAA,QACX,UAAU,WAAW,MAAiB,SAAS,WAAW,IAAI,CAAC,IAAI;AAAA,MACrE;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,SAAK,6BAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;AAAA,EAC9E;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,cACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,QAAQ,KAAK,IAAI;AAAA,MACnC;AAAA,MACF,iBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,IAAI;AAAA,MACtC;AAAA,MACF,gBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,UAAU,KAAK,IAAI;AAAA,MACrC;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,SAAS,MAAM,KAAK,OAAO,SAAS,gBAAgB;AAAA,MACpD,SAAS,MAAM,KAAK,OAAO,SAAS,gBAAgB;AAAA,IACtD;AAAA,EACF;AACF,CAAC;;;ADrFD,IAAO,gBAAQ;", "names": []}