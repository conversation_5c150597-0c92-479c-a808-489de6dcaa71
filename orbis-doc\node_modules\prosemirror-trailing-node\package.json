{"name": "prosemirror-trailing-node", "version": "3.0.0", "description": "A trailing node plugin for the prosemirror editor.", "homepage": "https://github.com/remirror/remirror/tree/HEAD/packages/prosemirror-trailing-node", "repository": {"type": "git", "url": "https://github.com/remirror/remirror.git", "directory": "packages/prosemirror-trailing-node"}, "license": "MIT", "contributors": ["Ifiok Jr. <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": {".": {"types": "./dist/prosemirror-trailing-node.d.ts", "import": "./dist/prosemirror-trailing-node.js", "require": "./dist/prosemirror-trailing-node.cjs"}, "./package.json": "./package.json"}, "main": "./dist/prosemirror-trailing-node.cjs", "module": "./dist/prosemirror-trailing-node.js", "types": "./dist/prosemirror-trailing-node.d.ts", "files": ["dist", "dist-types"], "dependencies": {"@remirror/core-constants": "3.0.0", "escape-string-regexp": "^4.0.0"}, "devDependencies": {"@remirror/cli": "1.1.0", "prosemirror-model": "^1.22.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.33.8"}, "peerDependencies": {"prosemirror-model": "^1.22.1", "prosemirror-state": "^1.4.2", "prosemirror-view": "^1.33.8"}, "peerDependenciesMeta": {}, "publishConfig": {"access": "public"}, "@remirror": {"sizeLimit": "10 KB"}, "scripts": {"build": "remirror-cli build"}}