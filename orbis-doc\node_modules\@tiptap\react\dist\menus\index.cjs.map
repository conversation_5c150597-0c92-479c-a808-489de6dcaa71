{"version": 3, "sources": ["../../src/menus/index.ts", "../../src/menus/BubbleMenu.tsx", "../../src/menus/FloatingMenu.tsx"], "sourcesContent": ["export * from './BubbleMenu.js'\nexport * from './FloatingMenu.js'\n", "import { type BubbleMenuPluginProps, BubbleMenuPlugin } from '@tiptap/extension-bubble-menu'\nimport { useCurrentEditor } from '@tiptap/react'\nimport React, { useEffect, useRef } from 'react'\nimport { createPortal } from 'react-dom'\n\ntype Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>\n\nexport type BubbleMenuProps = Optional<Omit<Optional<BubbleMenuPluginProps, 'pluginKey'>, 'element'>, 'editor'> &\n  React.HTMLAttributes<HTMLDivElement>\n\nexport const BubbleMenu = React.forwardRef<HTMLDivElement, BubbleMenuProps>(\n  (\n    { pluginKey = 'bubbleMenu', editor, updateDelay, resizeDelay, shouldShow = null, options, children, ...restProps },\n    ref,\n  ) => {\n    const menuEl = useRef(document.createElement('div'))\n\n    if (typeof ref === 'function') {\n      ref(menuEl.current)\n    } else if (ref) {\n      ref.current = menuEl.current\n    }\n\n    const { editor: currentEditor } = useCurrentEditor()\n\n    useEffect(() => {\n      const bubbleMenuElement = menuEl.current\n\n      bubbleMenuElement.style.visibility = 'hidden'\n      bubbleMenuElement.style.position = 'absolute'\n\n      if (editor?.isDestroyed || (currentEditor as any)?.isDestroyed) {\n        return\n      }\n\n      const attachToEditor = editor || currentEditor\n\n      if (!attachToEditor) {\n        console.warn('BubbleMenu component is not rendered inside of an editor component or does not have editor prop.')\n        return\n      }\n\n      const plugin = BubbleMenuPlugin({\n        updateDelay,\n        resizeDelay,\n        editor: attachToEditor,\n        element: bubbleMenuElement,\n        pluginKey,\n        shouldShow,\n        options,\n      })\n\n      attachToEditor.registerPlugin(plugin)\n\n      return () => {\n        attachToEditor.unregisterPlugin(pluginKey)\n        window.requestAnimationFrame(() => {\n          if (bubbleMenuElement.parentNode) {\n            bubbleMenuElement.parentNode.removeChild(bubbleMenuElement)\n          }\n        })\n      }\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [editor, currentEditor])\n\n    return createPortal(<div {...restProps}>{children}</div>, menuEl.current)\n  },\n)\n", "import type { FloatingMenuPluginProps } from '@tiptap/extension-floating-menu'\nimport { FloatingMenuPlugin } from '@tiptap/extension-floating-menu'\nimport { useCurrentEditor } from '@tiptap/react'\nimport React, { useEffect, useRef } from 'react'\nimport { createPortal } from 'react-dom'\n\ntype Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>\n\nexport type FloatingMenuProps = Omit<Optional<FloatingMenuPluginProps, 'pluginKey'>, 'element' | 'editor'> & {\n  editor: FloatingMenuPluginProps['editor'] | null\n  options?: FloatingMenuPluginProps['options']\n} & React.HTMLAttributes<HTMLDivElement>\n\nexport const FloatingMenu = React.forwardRef<HTMLDivElement, FloatingMenuProps>(\n  ({ pluginKey = 'floatingMenu', editor, shouldShow = null, options, children, ...restProps }, ref) => {\n    const menuEl = useRef(document.createElement('div'))\n\n    if (typeof ref === 'function') {\n      ref(menuEl.current)\n    } else if (ref) {\n      ref.current = menuEl.current\n    }\n\n    const { editor: currentEditor } = useCurrentEditor()\n\n    useEffect(() => {\n      const floatingMenuElement = menuEl.current\n\n      floatingMenuElement.style.visibility = 'hidden'\n      floatingMenuElement.style.position = 'absolute'\n\n      if (editor?.isDestroyed || (currentEditor as any)?.isDestroyed) {\n        return\n      }\n\n      const attachToEditor = editor || currentEditor\n\n      if (!attachToEditor) {\n        console.warn(\n          'FloatingMenu component is not rendered inside of an editor component or does not have editor prop.',\n        )\n        return\n      }\n\n      const plugin = FloatingMenuPlugin({\n        editor: attachToEditor,\n        element: floatingMenuElement,\n        pluginKey,\n        shouldShow,\n        options,\n      })\n\n      attachToEditor.registerPlugin(plugin)\n\n      return () => {\n        attachToEditor.unregisterPlugin(pluginKey)\n        window.requestAnimationFrame(() => {\n          if (floatingMenuElement.parentNode) {\n            floatingMenuElement.parentNode.removeChild(floatingMenuElement)\n          }\n        })\n      }\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [editor, currentEditor])\n\n    return createPortal(<div {...restProps}>{children}</div>, menuEl.current)\n  },\n)\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,mCAA6D;AAC7D,mBAAiC;AACjC,IAAAA,gBAAyC;AACzC,uBAA6B;AA8DL;AAvDjB,IAAM,aAAa,cAAAC,QAAM;AAAA,EAC9B,CACE,EAAE,YAAY,cAAc,QAAQ,aAAa,aAAa,aAAa,MAAM,SAAS,UAAU,GAAG,UAAU,GACjH,QACG;AACH,UAAM,aAAS,sBAAO,SAAS,cAAc,KAAK,CAAC;AAEnD,QAAI,OAAO,QAAQ,YAAY;AAC7B,UAAI,OAAO,OAAO;AAAA,IACpB,WAAW,KAAK;AACd,UAAI,UAAU,OAAO;AAAA,IACvB;AAEA,UAAM,EAAE,QAAQ,cAAc,QAAI,+BAAiB;AAEnD,iCAAU,MAAM;AACd,YAAM,oBAAoB,OAAO;AAEjC,wBAAkB,MAAM,aAAa;AACrC,wBAAkB,MAAM,WAAW;AAEnC,WAAI,iCAAQ,iBAAgB,+CAAuB,cAAa;AAC9D;AAAA,MACF;AAEA,YAAM,iBAAiB,UAAU;AAEjC,UAAI,CAAC,gBAAgB;AACnB,gBAAQ,KAAK,kGAAkG;AAC/G;AAAA,MACF;AAEA,YAAM,aAAS,+CAAiB;AAAA,QAC9B;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,qBAAe,eAAe,MAAM;AAEpC,aAAO,MAAM;AACX,uBAAe,iBAAiB,SAAS;AACzC,eAAO,sBAAsB,MAAM;AACjC,cAAI,kBAAkB,YAAY;AAChC,8BAAkB,WAAW,YAAY,iBAAiB;AAAA,UAC5D;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IAEF,GAAG,CAAC,QAAQ,aAAa,CAAC;AAE1B,eAAO,+BAAa,4CAAC,SAAK,GAAG,WAAY,UAAS,GAAQ,OAAO,OAAO;AAAA,EAC1E;AACF;;;AClEA,qCAAmC;AACnC,IAAAC,gBAAiC;AACjC,IAAAA,gBAAyC;AACzC,IAAAC,oBAA6B;AA6DL,IAAAC,sBAAA;AApDjB,IAAM,eAAe,cAAAC,QAAM;AAAA,EAChC,CAAC,EAAE,YAAY,gBAAgB,QAAQ,aAAa,MAAM,SAAS,UAAU,GAAG,UAAU,GAAG,QAAQ;AACnG,UAAM,aAAS,sBAAO,SAAS,cAAc,KAAK,CAAC;AAEnD,QAAI,OAAO,QAAQ,YAAY;AAC7B,UAAI,OAAO,OAAO;AAAA,IACpB,WAAW,KAAK;AACd,UAAI,UAAU,OAAO;AAAA,IACvB;AAEA,UAAM,EAAE,QAAQ,cAAc,QAAI,gCAAiB;AAEnD,iCAAU,MAAM;AACd,YAAM,sBAAsB,OAAO;AAEnC,0BAAoB,MAAM,aAAa;AACvC,0BAAoB,MAAM,WAAW;AAErC,WAAI,iCAAQ,iBAAgB,+CAAuB,cAAa;AAC9D;AAAA,MACF;AAEA,YAAM,iBAAiB,UAAU;AAEjC,UAAI,CAAC,gBAAgB;AACnB,gBAAQ;AAAA,UACN;AAAA,QACF;AACA;AAAA,MACF;AAEA,YAAM,aAAS,mDAAmB;AAAA,QAChC,QAAQ;AAAA,QACR,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,qBAAe,eAAe,MAAM;AAEpC,aAAO,MAAM;AACX,uBAAe,iBAAiB,SAAS;AACzC,eAAO,sBAAsB,MAAM;AACjC,cAAI,oBAAoB,YAAY;AAClC,gCAAoB,WAAW,YAAY,mBAAmB;AAAA,UAChE;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IAEF,GAAG,CAAC,QAAQ,aAAa,CAAC;AAE1B,eAAO,gCAAa,6CAAC,SAAK,GAAG,WAAY,UAAS,GAAQ,OAAO,OAAO;AAAA,EAC1E;AACF;", "names": ["import_react", "React", "import_react", "import_react_dom", "import_jsx_runtime", "React"]}