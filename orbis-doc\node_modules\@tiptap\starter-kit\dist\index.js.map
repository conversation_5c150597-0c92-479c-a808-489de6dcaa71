{"version": 3, "sources": ["../src/starter-kit.ts", "../src/index.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\nimport type { BlockquoteOptions } from '@tiptap/extension-blockquote'\nimport { Blockquote } from '@tiptap/extension-blockquote'\nimport type { BoldOptions } from '@tiptap/extension-bold'\nimport { Bold } from '@tiptap/extension-bold'\nimport type { CodeOptions } from '@tiptap/extension-code'\nimport { Code } from '@tiptap/extension-code'\nimport type { CodeBlockOptions } from '@tiptap/extension-code-block'\nimport { CodeBlock } from '@tiptap/extension-code-block'\nimport { Document } from '@tiptap/extension-document'\nimport type { HardBreakOptions } from '@tiptap/extension-hard-break'\nimport { HardBreak } from '@tiptap/extension-hard-break'\nimport type { HeadingOptions } from '@tiptap/extension-heading'\nimport { Heading } from '@tiptap/extension-heading'\nimport type { HorizontalRuleOptions } from '@tiptap/extension-horizontal-rule'\nimport { HorizontalRule } from '@tiptap/extension-horizontal-rule'\nimport type { ItalicOptions } from '@tiptap/extension-italic'\nimport { Italic } from '@tiptap/extension-italic'\nimport type { LinkOptions } from '@tiptap/extension-link'\nimport { Link } from '@tiptap/extension-link'\nimport type { BulletListOptions, ListItemOptions, ListKeymapOptions, OrderedListOptions } from '@tiptap/extension-list'\nimport { BulletList, ListItem, ListKeymap, OrderedList } from '@tiptap/extension-list'\nimport type { ParagraphOptions } from '@tiptap/extension-paragraph'\nimport { Paragraph } from '@tiptap/extension-paragraph'\nimport type { StrikeOptions } from '@tiptap/extension-strike'\nimport { Strike } from '@tiptap/extension-strike'\nimport { Text } from '@tiptap/extension-text'\nimport type { UnderlineOptions } from '@tiptap/extension-underline'\nimport { Underline } from '@tiptap/extension-underline'\nimport type { DropcursorOptions, TrailingNodeOptions, UndoRedoOptions } from '@tiptap/extensions'\nimport { Dropcursor, Gapcursor, TrailingNode, UndoRedo } from '@tiptap/extensions'\n\nexport interface StarterKitOptions {\n  /**\n   * If set to false, the blockquote extension will not be registered\n   * @example blockquote: false\n   */\n  blockquote: Partial<BlockquoteOptions> | false\n\n  /**\n   * If set to false, the bold extension will not be registered\n   * @example bold: false\n   */\n  bold: Partial<BoldOptions> | false\n\n  /**\n   * If set to false, the bulletList extension will not be registered\n   * @example bulletList: false\n   */\n  bulletList: Partial<BulletListOptions> | false\n\n  /**\n   * If set to false, the code extension will not be registered\n   * @example code: false\n   */\n  code: Partial<CodeOptions> | false\n\n  /**\n   * If set to false, the codeBlock extension will not be registered\n   * @example codeBlock: false\n   */\n  codeBlock: Partial<CodeBlockOptions> | false\n\n  /**\n   * If set to false, the document extension will not be registered\n   * @example document: false\n   */\n  document: false\n\n  /**\n   * If set to false, the dropcursor extension will not be registered\n   * @example dropcursor: false\n   */\n  dropcursor: Partial<DropcursorOptions> | false\n\n  /**\n   * If set to false, the gapcursor extension will not be registered\n   * @example gapcursor: false\n   */\n  gapcursor: false\n\n  /**\n   * If set to false, the hardBreak extension will not be registered\n   * @example hardBreak: false\n   */\n  hardBreak: Partial<HardBreakOptions> | false\n\n  /**\n   * If set to false, the heading extension will not be registered\n   * @example heading: false\n   */\n  heading: Partial<HeadingOptions> | false\n\n  /**\n   * If set to false, the undo-redo extension will not be registered\n   * @example undoRedo: false\n   */\n  undoRedo: Partial<UndoRedoOptions> | false\n\n  /**\n   * If set to false, the horizontalRule extension will not be registered\n   * @example horizontalRule: false\n   */\n  horizontalRule: Partial<HorizontalRuleOptions> | false\n\n  /**\n   * If set to false, the italic extension will not be registered\n   * @example italic: false\n   */\n  italic: Partial<ItalicOptions> | false\n\n  /**\n   * If set to false, the listItem extension will not be registered\n   * @example listItem: false\n   */\n  listItem: Partial<ListItemOptions> | false\n\n  /**\n   * If set to false, the listItemKeymap extension will not be registered\n   * @example listKeymap: false\n   */\n  listKeymap: Partial<ListKeymapOptions> | false\n\n  /**\n   * If set to false, the link extension will not be registered\n   * @example link: false\n   */\n  link: Partial<LinkOptions> | false\n\n  /**\n   * If set to false, the orderedList extension will not be registered\n   * @example orderedList: false\n   */\n  orderedList: Partial<OrderedListOptions> | false\n\n  /**\n   * If set to false, the paragraph extension will not be registered\n   * @example paragraph: false\n   */\n  paragraph: Partial<ParagraphOptions> | false\n\n  /**\n   * If set to false, the strike extension will not be registered\n   * @example strike: false\n   */\n  strike: Partial<StrikeOptions> | false\n\n  /**\n   * If set to false, the text extension will not be registered\n   * @example text: false\n   */\n  text: false\n\n  /**\n   * If set to false, the underline extension will not be registered\n   * @example underline: false\n   */\n  underline: Partial<UnderlineOptions> | false\n\n  /**\n   * If set to false, the trailingNode extension will not be registered\n   * @example trailingNode: false\n   */\n  trailingNode: Partial<TrailingNodeOptions> | false\n}\n\n/**\n * The starter kit is a collection of essential editor extensions.\n *\n * It’s a good starting point for building your own editor.\n */\nexport const StarterKit = Extension.create<StarterKitOptions>({\n  name: 'starterKit',\n\n  addExtensions() {\n    const extensions = []\n\n    if (this.options.bold !== false) {\n      extensions.push(Bold.configure(this.options.bold))\n    }\n\n    if (this.options.blockquote !== false) {\n      extensions.push(Blockquote.configure(this.options.blockquote))\n    }\n\n    if (this.options.bulletList !== false) {\n      extensions.push(BulletList.configure(this.options.bulletList))\n    }\n\n    if (this.options.code !== false) {\n      extensions.push(Code.configure(this.options.code))\n    }\n\n    if (this.options.codeBlock !== false) {\n      extensions.push(CodeBlock.configure(this.options.codeBlock))\n    }\n\n    if (this.options.document !== false) {\n      extensions.push(Document.configure(this.options.document))\n    }\n\n    if (this.options.dropcursor !== false) {\n      extensions.push(Dropcursor.configure(this.options.dropcursor))\n    }\n\n    if (this.options.gapcursor !== false) {\n      extensions.push(Gapcursor.configure(this.options.gapcursor))\n    }\n\n    if (this.options.hardBreak !== false) {\n      extensions.push(HardBreak.configure(this.options.hardBreak))\n    }\n\n    if (this.options.heading !== false) {\n      extensions.push(Heading.configure(this.options.heading))\n    }\n\n    if (this.options.undoRedo !== false) {\n      extensions.push(UndoRedo.configure(this.options.undoRedo))\n    }\n\n    if (this.options.horizontalRule !== false) {\n      extensions.push(HorizontalRule.configure(this.options.horizontalRule))\n    }\n\n    if (this.options.italic !== false) {\n      extensions.push(Italic.configure(this.options.italic))\n    }\n\n    if (this.options.listItem !== false) {\n      extensions.push(ListItem.configure(this.options.listItem))\n    }\n\n    if (this.options.listKeymap !== false) {\n      extensions.push(ListKeymap.configure(this.options?.listKeymap))\n    }\n\n    if (this.options.link !== false) {\n      extensions.push(Link.configure(this.options?.link))\n    }\n\n    if (this.options.orderedList !== false) {\n      extensions.push(OrderedList.configure(this.options.orderedList))\n    }\n\n    if (this.options.paragraph !== false) {\n      extensions.push(Paragraph.configure(this.options.paragraph))\n    }\n\n    if (this.options.strike !== false) {\n      extensions.push(Strike.configure(this.options.strike))\n    }\n\n    if (this.options.text !== false) {\n      extensions.push(Text.configure(this.options.text))\n    }\n\n    if (this.options.underline !== false) {\n      extensions.push(Underline.configure(this.options?.underline))\n    }\n\n    if (this.options.trailingNode !== false) {\n      extensions.push(TrailingNode.configure(this.options?.trailingNode))\n    }\n\n    return extensions\n  },\n})\n", "import { StarterKit } from './starter-kit.js'\n\nexport type { StarterKitOptions } from './starter-kit.js'\nexport * from './starter-kit.js'\n\nexport default StarterKit\n"], "mappings": ";AAAA,SAAS,iBAAiB;AAE1B,SAAS,kBAAkB;AAE3B,SAAS,YAAY;AAErB,SAAS,YAAY;AAErB,SAAS,iBAAiB;AAC1B,SAAS,gBAAgB;AAEzB,SAAS,iBAAiB;AAE1B,SAAS,eAAe;AAExB,SAAS,sBAAsB;AAE/B,SAAS,cAAc;AAEvB,SAAS,YAAY;AAErB,SAAS,YAAY,UAAU,YAAY,mBAAmB;AAE9D,SAAS,iBAAiB;AAE1B,SAAS,cAAc;AACvB,SAAS,YAAY;AAErB,SAAS,iBAAiB;AAE1B,SAAS,YAAY,WAAW,cAAc,gBAAgB;AA6IvD,IAAM,aAAa,UAAU,OAA0B;AAAA,EAC5D,MAAM;AAAA,EAEN,gBAAgB;AA9KlB;AA+KI,UAAM,aAAa,CAAC;AAEpB,QAAI,KAAK,QAAQ,SAAS,OAAO;AAC/B,iBAAW,KAAK,KAAK,UAAU,KAAK,QAAQ,IAAI,CAAC;AAAA,IACnD;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;AAAA,IAC/D;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;AAAA,IAC/D;AAEA,QAAI,KAAK,QAAQ,SAAS,OAAO;AAC/B,iBAAW,KAAK,KAAK,UAAU,KAAK,QAAQ,IAAI,CAAC;AAAA,IACnD;AAEA,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;AAAA,IAC7D;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;AAAA,IAC3D;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;AAAA,IAC/D;AAEA,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;AAAA,IAC7D;AAEA,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;AAAA,IAC7D;AAEA,QAAI,KAAK,QAAQ,YAAY,OAAO;AAClC,iBAAW,KAAK,QAAQ,UAAU,KAAK,QAAQ,OAAO,CAAC;AAAA,IACzD;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;AAAA,IAC3D;AAEA,QAAI,KAAK,QAAQ,mBAAmB,OAAO;AACzC,iBAAW,KAAK,eAAe,UAAU,KAAK,QAAQ,cAAc,CAAC;AAAA,IACvE;AAEA,QAAI,KAAK,QAAQ,WAAW,OAAO;AACjC,iBAAW,KAAK,OAAO,UAAU,KAAK,QAAQ,MAAM,CAAC;AAAA,IACvD;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;AAAA,IAC3D;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,WAAU,UAAK,YAAL,mBAAc,UAAU,CAAC;AAAA,IAChE;AAEA,QAAI,KAAK,QAAQ,SAAS,OAAO;AAC/B,iBAAW,KAAK,KAAK,WAAU,UAAK,YAAL,mBAAc,IAAI,CAAC;AAAA,IACpD;AAEA,QAAI,KAAK,QAAQ,gBAAgB,OAAO;AACtC,iBAAW,KAAK,YAAY,UAAU,KAAK,QAAQ,WAAW,CAAC;AAAA,IACjE;AAEA,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;AAAA,IAC7D;AAEA,QAAI,KAAK,QAAQ,WAAW,OAAO;AACjC,iBAAW,KAAK,OAAO,UAAU,KAAK,QAAQ,MAAM,CAAC;AAAA,IACvD;AAEA,QAAI,KAAK,QAAQ,SAAS,OAAO;AAC/B,iBAAW,KAAK,KAAK,UAAU,KAAK,QAAQ,IAAI,CAAC;AAAA,IACnD;AAEA,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,WAAU,UAAK,YAAL,mBAAc,SAAS,CAAC;AAAA,IAC9D;AAEA,QAAI,KAAK,QAAQ,iBAAiB,OAAO;AACvC,iBAAW,KAAK,aAAa,WAAU,UAAK,YAAL,mBAAc,YAAY,CAAC;AAAA,IACpE;AAEA,WAAO;AAAA,EACT;AACF,CAAC;;;ACtQD,IAAO,gBAAQ;", "names": []}