{"name": "prosemirror-changeset", "version": "2.3.1", "description": "Distills a series of editing steps into deleted and added ranges", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "sideEffects": false, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}], "repository": {"type": "git", "url": "git://github.com/prosemirror/prosemirror-changeset.git"}, "dependencies": {"prosemirror-transform": "^1.0.0"}, "devDependencies": {"@prosemirror/buildhelper": "^0.1.5", "prosemirror-model": "^1.0.0", "prosemirror-test-builder": "^1.0.0", "builddocs": "^1.0.8"}, "scripts": {"test": "pm-runtests", "prepare": "pm-buildhelper src/changeset.ts", "build-readme": "builddocs --format markdown --main src/README.md src/changeset.ts  > README.md"}}