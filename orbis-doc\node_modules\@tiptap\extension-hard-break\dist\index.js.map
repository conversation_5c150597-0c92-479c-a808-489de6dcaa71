{"version": 3, "sources": ["../src/hard-break.ts", "../src/index.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface HardBreakOptions {\n  /**\n   * Controls if marks should be kept after being split by a hard break.\n   * @default true\n   * @example false\n   */\n  keepMarks: boolean\n\n  /**\n   * HTML attributes to add to the hard break element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    hardBreak: {\n      /**\n       * Add a hard break\n       * @example editor.commands.setHardBreak()\n       */\n      setHardBreak: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to insert hard breaks.\n * @see https://www.tiptap.dev/api/nodes/hard-break\n */\nexport const HardBreak = Node.create<HardBreakOptions>({\n  name: 'hardBreak',\n\n  addOptions() {\n    return {\n      keepMarks: true,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline: true,\n\n  group: 'inline',\n\n  selectable: false,\n\n  linebreakReplacement: true,\n\n  parseHTML() {\n    return [{ tag: 'br' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['br', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  renderText() {\n    return '\\n'\n  },\n\n  addCommands() {\n    return {\n      setHardBreak:\n        () =>\n        ({ commands, chain, state, editor }) => {\n          return commands.first([\n            () => commands.exitCode(),\n            () =>\n              commands.command(() => {\n                const { selection, storedMarks } = state\n\n                if (selection.$from.parent.type.spec.isolating) {\n                  return false\n                }\n\n                const { keepMarks } = this.options\n                const { splittableMarks } = editor.extensionManager\n                const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n                return chain()\n                  .insertContent({ type: this.name })\n                  .command(({ tr, dispatch }) => {\n                    if (dispatch && marks && keepMarks) {\n                      const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n                      tr.ensureMarks(filteredMarks)\n                    }\n\n                    return true\n                  })\n                  .run()\n              }),\n          ])\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Enter': () => this.editor.commands.setHardBreak(),\n      'Shift-Enter': () => this.editor.commands.setHardBreak(),\n    }\n  },\n})\n", "import { HardBreak } from './hard-break.js'\n\nexport * from './hard-break.js'\n\nexport default HardBreak\n"], "mappings": ";AAAA,SAAS,iBAAiB,YAAY;AAkC/B,IAAM,YAAY,KAAK,OAAyB;AAAA,EACrD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,WAAW;AAAA,MACX,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,QAAQ;AAAA,EAER,OAAO;AAAA,EAEP,YAAY;AAAA,EAEZ,sBAAsB;AAAA,EAEtB,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,KAAK,CAAC;AAAA,EACvB;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,CAAC;AAAA,EAC5E;AAAA,EAEA,aAAa;AACX,WAAO;AAAA,EACT;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,cACE,MACA,CAAC,EAAE,UAAU,OAAO,OAAO,OAAO,MAAM;AACtC,eAAO,SAAS,MAAM;AAAA,UACpB,MAAM,SAAS,SAAS;AAAA,UACxB,MACE,SAAS,QAAQ,MAAM;AACrB,kBAAM,EAAE,WAAW,YAAY,IAAI;AAEnC,gBAAI,UAAU,MAAM,OAAO,KAAK,KAAK,WAAW;AAC9C,qBAAO;AAAA,YACT;AAEA,kBAAM,EAAE,UAAU,IAAI,KAAK;AAC3B,kBAAM,EAAE,gBAAgB,IAAI,OAAO;AACnC,kBAAM,QAAQ,eAAgB,UAAU,IAAI,gBAAgB,UAAU,MAAM,MAAM;AAElF,mBAAO,MAAM,EACV,cAAc,EAAE,MAAM,KAAK,KAAK,CAAC,EACjC,QAAQ,CAAC,EAAE,IAAI,SAAS,MAAM;AAC7B,kBAAI,YAAY,SAAS,WAAW;AAClC,sBAAM,gBAAgB,MAAM,OAAO,UAAQ,gBAAgB,SAAS,KAAK,KAAK,IAAI,CAAC;AAEnF,mBAAG,YAAY,aAAa;AAAA,cAC9B;AAEA,qBAAO;AAAA,YACT,CAAC,EACA,IAAI;AAAA,UACT,CAAC;AAAA,QACL,CAAC;AAAA,MACH;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,aAAa,MAAM,KAAK,OAAO,SAAS,aAAa;AAAA,MACrD,eAAe,MAAM,KAAK,OAAO,SAAS,aAAa;AAAA,IACzD;AAAA,EACF;AACF,CAAC;;;ACvGD,IAAO,gBAAQ;", "names": []}