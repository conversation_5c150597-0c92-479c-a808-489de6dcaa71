{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/DEV/orbis-doc/src/components/DocumentEditor.tsx"], "sourcesContent": ["'use client'\n\nimport { useEditor, EditorContent } from '@tiptap/react'\nimport StarterKit from '@tiptap/starter-kit'\nimport { useEffect, useState } from 'react'\nimport { PaginationPlus } from 'tiptap-pagination-plus'\nimport '../styles/editor.css'\n\ninterface DocumentEditorProps {\n  content?: string\n  onChange?: (content: string) => void\n}\n\nexport default function DocumentEditor({ content = '', onChange }: DocumentEditorProps) {\n  const [isMounted, setIsMounted] = useState(false)\n\n  const editor = useEditor({\n    extensions: [\n      StarterKit,\n      PaginationPlus.configure({\n        pageSize: {\n          width: 210, // A4 width in mm\n          height: 297, // A4 height in mm\n        },\n        pageMargins: {\n          top: 25,\n          bottom: 25,\n          left: 25,\n          right: 25,\n        },\n        headerHeight: 20,\n        footerHeight: 20,\n        showPageNumbers: true,\n        pageNumberPosition: 'bottom-center',\n      }),\n    ],\n    content,\n    immediatelyRender: false, // Éviter les erreurs d'hydratation SSR\n    onUpdate: ({ editor }) => {\n      const html = editor.getHTML()\n      onChange?.(html)\n    },\n    editorProps: {\n      attributes: {\n        class: 'tiptap-editor focus:outline-none',\n      },\n    },\n  })\n\n  // Gérer l'hydratation côté client\n  useEffect(() => {\n    setIsMounted(true)\n  }, [])\n\n  // Éviter les erreurs d'hydratation\n  if (!isMounted) {\n    return (\n      <div className=\"min-h-screen bg-gray-100 p-8\">\n        <div className=\"max-w-4xl mx-auto bg-white shadow-lg\">\n          <div className=\"h-96 flex items-center justify-center text-gray-500\">\n            Chargement de l'éditeur...\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-100 p-8\">\n      <div className=\"max-w-5xl mx-auto\">\n        {/* Conteneur de l'éditeur avec pagination */}\n        <EditorContent editor={editor} />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;;;AALA;;;;;;AAae,SAAS,eAAe,KAA+C;QAA/C,EAAE,UAAU,EAAE,EAAE,QAAQ,EAAuB,GAA/C;;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE;QACvB,YAAY;YACV,8JAAA,CAAA,UAAU;YACV,yKAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;gBACvB,UAAU;oBACR,OAAO;oBACP,QAAQ;gBACV;gBACA,aAAa;oBACX,KAAK;oBACL,QAAQ;oBACR,MAAM;oBACN,OAAO;gBACT;gBACA,cAAc;gBACd,cAAc;gBACd,iBAAiB;gBACjB,oBAAoB;YACtB;SACD;QACD;QACA,mBAAmB;QACnB,QAAQ;gDAAE;oBAAC,EAAE,MAAM,EAAE;gBACnB,MAAM,OAAO,OAAO,OAAO;gBAC3B,qBAAA,+BAAA,SAAW;YACb;;QACA,aAAa;YACX,YAAY;gBACV,OAAO;YACT;QACF;IACF;IAEA,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,aAAa;QACf;mCAAG,EAAE;IAEL,mCAAmC;IACnC,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAsD;;;;;;;;;;;;;;;;IAM7E;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBAEb,cAAA,6LAAC,qKAAA,CAAA,gBAAa;gBAAC,QAAQ;;;;;;;;;;;;;;;;AAI/B;GA9DwB;;QAGP,qKAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}