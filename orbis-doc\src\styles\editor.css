/* Styles pour l'éditeur Tiptap avec pagination */

.tiptap-editor {
  outline: none;
  border: none;
}

/* Styles pour les pages A4 */
.tiptap-pagination-page {
  width: 210mm;
  min-height: 297mm;
  margin: 0 auto 20px auto;
  background: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 25mm;
  box-sizing: border-box;
  position: relative;
}

/* Styles pour l'en-tête */
.tiptap-pagination-header {
  position: absolute;
  top: 10mm;
  left: 25mm;
  right: 25mm;
  height: 15mm;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #6b7280;
}

/* Styles pour le pied de page */
.tiptap-pagination-footer {
  position: absolute;
  bottom: 10mm;
  left: 25mm;
  right: 25mm;
  height: 15mm;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #6b7280;
}

/* Styles pour le contenu de la page */
.tiptap-pagination-content {
  min-height: calc(297mm - 50mm); /* Hauteur totale moins marges top/bottom */
  padding-top: 20mm; /* Espace pour l'en-tête */
  padding-bottom: 20mm; /* Espace pour le pied de page */
}

/* Styles généraux pour le contenu */
.tiptap-editor p {
  margin: 0 0 1em 0;
  line-height: 1.6;
}

.tiptap-editor h1,
.tiptap-editor h2,
.tiptap-editor h3,
.tiptap-editor h4,
.tiptap-editor h5,
.tiptap-editor h6 {
  margin: 1.5em 0 0.5em 0;
  font-weight: bold;
}

.tiptap-editor h1 { font-size: 2em; }
.tiptap-editor h2 { font-size: 1.5em; }
.tiptap-editor h3 { font-size: 1.25em; }

.tiptap-editor ul,
.tiptap-editor ol {
  margin: 1em 0;
  padding-left: 2em;
}

.tiptap-editor blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
}

.tiptap-editor code {
  background: #f3f4f6;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.tiptap-editor pre {
  background: #f3f4f6;
  padding: 1em;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1em 0;
}

.tiptap-editor pre code {
  background: none;
  padding: 0;
}

/* Responsive pour l'aperçu */
@media screen and (max-width: 768px) {
  .tiptap-pagination-page {
    width: 100%;
    min-height: auto;
    margin: 0 0 20px 0;
    padding: 20px;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
  
  .tiptap-pagination-header,
  .tiptap-pagination-footer {
    position: static;
    margin: 10px 0;
  }
  
  .tiptap-pagination-content {
    min-height: auto;
    padding: 0;
  }
}
