{"name": "prosemirror-gapcursor", "version": "1.3.2", "description": "ProseMirror plugin for cursors at normally impossible-to-reach positions", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./style/gapcursor.css": "./style/gapcursor.css"}, "sideEffects": ["./style/gapcursor.css"], "style": "style/gapcursor.css", "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}], "repository": {"type": "git", "url": "git://github.com/prosemirror/prosemirror-gapcursor.git"}, "dependencies": {"prosemirror-keymap": "^1.0.0", "prosemirror-model": "^1.0.0", "prosemirror-state": "^1.0.0", "prosemirror-view": "^1.0.0"}, "devDependencies": {"@prosemirror/buildhelper": "^0.1.5"}, "scripts": {"prepare": "pm-buildhelper src/index.ts"}}