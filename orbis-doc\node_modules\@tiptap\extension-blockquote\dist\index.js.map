{"version": 3, "sources": ["../src/blockquote.tsx", "../src/index.ts"], "sourcesContent": ["/** @jsxImportSource @tiptap/core */\nimport { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nexport interface BlockquoteOptions {\n  /**\n   * HTML attributes to add to the blockquote element\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    blockQuote: {\n      /**\n       * Set a blockquote node\n       */\n      setBlockquote: () => ReturnType\n      /**\n       * Toggle a blockquote node\n       */\n      toggleBlockquote: () => ReturnType\n      /**\n       * Unset a blockquote node\n       */\n      unsetBlockquote: () => ReturnType\n    }\n  }\n}\n\n/**\n * Matches a blockquote to a `>` as input.\n */\nexport const inputRegex = /^\\s*>\\s$/\n\n/**\n * This extension allows you to create blockquotes.\n * @see https://tiptap.dev/api/nodes/blockquote\n */\nexport const Blockquote = Node.create<BlockquoteOptions>({\n  name: 'blockquote',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'block+',\n\n  group: 'block',\n\n  defining: true,\n\n  parseHTML() {\n    return [{ tag: 'blockquote' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return (\n      <blockquote {...mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)}>\n        <slot />\n      </blockquote>\n    )\n  },\n\n  addCommands() {\n    return {\n      setBlockquote:\n        () =>\n        ({ commands }) => {\n          return commands.wrapIn(this.name)\n        },\n      toggleBlockquote:\n        () =>\n        ({ commands }) => {\n          return commands.toggleWrap(this.name)\n        },\n      unsetBlockquote:\n        () =>\n        ({ commands }) => {\n          return commands.lift(this.name)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-b': () => this.editor.commands.toggleBlockquote(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import { Blockquote } from './blockquote.jsx'\n\nexport * from './blockquote.jsx'\n\nexport default Blockquote\n"], "mappings": ";AACA,SAAS,iBAAiB,MAAM,yBAAyB;AA6DjD;AA5BD,IAAM,aAAa;AAMnB,IAAM,aAAa,KAAK,OAA0B;AAAA,EACvD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,EAET,OAAO;AAAA,EAEP,UAAU;AAAA,EAEV,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,aAAa,CAAC;AAAA,EAC/B;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WACE,oBAAC,gBAAY,GAAG,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GACzE,8BAAC,UAAK,GACR;AAAA,EAEJ;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,eACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,OAAO,KAAK,IAAI;AAAA,MAClC;AAAA,MACF,kBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,IAAI;AAAA,MACtC;AAAA,MACF,iBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,KAAK,KAAK,IAAI;AAAA,MAChC;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,eAAe,MAAM,KAAK,OAAO,SAAS,iBAAiB;AAAA,IAC7D;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,WAAO;AAAA,MACL,kBAAkB;AAAA,QAChB,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACjGD,IAAO,gBAAQ;", "names": []}