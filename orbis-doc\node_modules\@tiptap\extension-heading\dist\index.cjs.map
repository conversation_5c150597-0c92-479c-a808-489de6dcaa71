{"version": 3, "sources": ["../src/index.ts", "../src/heading.ts"], "sourcesContent": ["import { Heading } from './heading.js'\n\nexport * from './heading.js'\n\nexport default Heading\n", "import { mergeAttributes, Node, textblockTypeInputRule } from '@tiptap/core'\n\n/**\n * The heading level options.\n */\nexport type Level = 1 | 2 | 3 | 4 | 5 | 6\n\nexport interface HeadingOptions {\n  /**\n   * The available heading levels.\n   * @default [1, 2, 3, 4, 5, 6]\n   * @example [1, 2, 3]\n   */\n  levels: Level[]\n\n  /**\n   * The HTML attributes for a heading node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    heading: {\n      /**\n       * Set a heading node\n       * @param attributes The heading attributes\n       * @example editor.commands.setHeading({ level: 1 })\n       */\n      setHeading: (attributes: { level: Level }) => ReturnType\n      /**\n       * Toggle a heading node\n       * @param attributes The heading attributes\n       * @example editor.commands.toggleHeading({ level: 1 })\n       */\n      toggleHeading: (attributes: { level: Level }) => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to create headings.\n * @see https://www.tiptap.dev/api/nodes/heading\n */\nexport const Heading = Node.create<HeadingOptions>({\n  name: 'heading',\n\n  addOptions() {\n    return {\n      levels: [1, 2, 3, 4, 5, 6],\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'inline*',\n\n  group: 'block',\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      level: {\n        default: 1,\n        rendered: false,\n      },\n    }\n  },\n\n  parseHTML() {\n    return this.options.levels.map((level: Level) => ({\n      tag: `h${level}`,\n      attrs: { level },\n    }))\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    const hasLevel = this.options.levels.includes(node.attrs.level)\n    const level = hasLevel ? node.attrs.level : this.options.levels[0]\n\n    return [`h${level}`, mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setHeading:\n        attributes =>\n        ({ commands }) => {\n          if (!this.options.levels.includes(attributes.level)) {\n            return false\n          }\n\n          return commands.setNode(this.name, attributes)\n        },\n      toggleHeading:\n        attributes =>\n        ({ commands }) => {\n          if (!this.options.levels.includes(attributes.level)) {\n            return false\n          }\n\n          return commands.toggleNode(this.name, 'paragraph', attributes)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return this.options.levels.reduce(\n      (items, level) => ({\n        ...items,\n        ...{\n          [`Mod-Alt-${level}`]: () => this.editor.commands.toggleHeading({ level }),\n        },\n      }),\n      {},\n    )\n  },\n\n  addInputRules() {\n    return this.options.levels.map(level => {\n      return textblockTypeInputRule({\n        find: new RegExp(`^(#{${Math.min(...this.options.levels)},${level}})\\\\s$`),\n        type: this.type,\n        getAttributes: {\n          level,\n        },\n      })\n    })\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,kBAA8D;AA8CvD,IAAM,UAAU,iBAAK,OAAuB;AAAA,EACjD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MACzB,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,EAET,OAAO;AAAA,EAEP,UAAU;AAAA,EAEV,gBAAgB;AACd,WAAO;AAAA,MACL,OAAO;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,QAAQ,OAAO,IAAI,CAAC,WAAkB;AAAA,MAChD,KAAK,IAAI,KAAK;AAAA,MACd,OAAO,EAAE,MAAM;AAAA,IACjB,EAAE;AAAA,EACJ;AAAA,EAEA,WAAW,EAAE,MAAM,eAAe,GAAG;AACnC,UAAM,WAAW,KAAK,QAAQ,OAAO,SAAS,KAAK,MAAM,KAAK;AAC9D,UAAM,QAAQ,WAAW,KAAK,MAAM,QAAQ,KAAK,QAAQ,OAAO,CAAC;AAEjE,WAAO,CAAC,IAAI,KAAK,QAAI,6BAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;AAAA,EACtF;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,YACE,gBACA,CAAC,EAAE,SAAS,MAAM;AAChB,YAAI,CAAC,KAAK,QAAQ,OAAO,SAAS,WAAW,KAAK,GAAG;AACnD,iBAAO;AAAA,QACT;AAEA,eAAO,SAAS,QAAQ,KAAK,MAAM,UAAU;AAAA,MAC/C;AAAA,MACF,eACE,gBACA,CAAC,EAAE,SAAS,MAAM;AAChB,YAAI,CAAC,KAAK,QAAQ,OAAO,SAAS,WAAW,KAAK,GAAG;AACnD,iBAAO;AAAA,QACT;AAEA,eAAO,SAAS,WAAW,KAAK,MAAM,aAAa,UAAU;AAAA,MAC/D;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO,KAAK,QAAQ,OAAO;AAAA,MACzB,CAAC,OAAO,WAAW;AAAA,QACjB,GAAG;AAAA,QACH,GAAG;AAAA,UACD,CAAC,WAAW,KAAK,EAAE,GAAG,MAAM,KAAK,OAAO,SAAS,cAAc,EAAE,MAAM,CAAC;AAAA,QAC1E;AAAA,MACF;AAAA,MACA,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK,QAAQ,OAAO,IAAI,WAAS;AACtC,iBAAO,oCAAuB;AAAA,QAC5B,MAAM,IAAI,OAAO,OAAO,KAAK,IAAI,GAAG,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,QAAQ;AAAA,QACzE,MAAM,KAAK;AAAA,QACX,eAAe;AAAA,UACb;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF,CAAC;;;AD/HD,IAAO,gBAAQ;", "names": []}