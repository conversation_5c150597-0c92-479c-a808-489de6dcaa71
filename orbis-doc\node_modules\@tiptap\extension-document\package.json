{"name": "@tiptap/extension-document", "description": "document extension for tiptap", "version": "3.0.7", "homepage": "https://tiptap.dev", "keywords": ["tiptap", "tiptap extension"], "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/ueberdosis"}, "type": "module", "exports": {".": {"types": {"import": "./dist/index.d.ts", "require": "./dist/index.d.cts"}, "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["src", "dist"], "devDependencies": {"@tiptap/core": "^3.0.7"}, "peerDependencies": {"@tiptap/core": "^3.0.7"}, "repository": {"type": "git", "url": "https://github.com/ueberdosis/tiptap", "directory": "packages/extension-document"}, "scripts": {"build": "tsup", "lint": "prettier ./src/ --check && eslint --cache --quiet --no-error-on-unmatched-pattern ./src/"}}