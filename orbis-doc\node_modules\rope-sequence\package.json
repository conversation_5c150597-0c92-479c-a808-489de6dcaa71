{"name": "rope-sequence", "version": "1.3.4", "description": "Rope-based persistent sequence type", "main": "dist/index.cjs", "type": "module", "module": "dist/index.js", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "scripts": {"build": "rollup -c", "prepare": "npm run build", "test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/marijnh/rope-sequence.git"}, "keywords": ["persistent", "data", "structure", "rope", "sequence"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@rollup/plugin-buble": "^0.20.0", "rollup": "^1.26.3"}}