{"version": 3, "sources": ["../src/index.ts", "../src/code-block.ts"], "sourcesContent": ["import { CodeBlock } from './code-block.js'\n\nexport * from './code-block.js'\n\nexport default CodeBlock\n", "import { mergeAttributes, Node, textblockTypeInputRule } from '@tiptap/core'\nimport { Plug<PERSON>, Plugin<PERSON>ey, Selection, TextSelection } from '@tiptap/pm/state'\n\nexport interface CodeBlockOptions {\n  /**\n   * Adds a prefix to language classes that are applied to code tags.\n   * @default 'language-'\n   */\n  languageClassPrefix: string\n  /**\n   * Define whether the node should be exited on triple enter.\n   * @default true\n   */\n  exitOnTripleEnter: boolean\n  /**\n   * Define whether the node should be exited on arrow down if there is no node after it.\n   * @default true\n   */\n  exitOnArrowDown: boolean\n  /**\n   * The default language.\n   * @default null\n   * @example 'js'\n   */\n  defaultLanguage: string | null | undefined\n  /**\n   * Custom HTML attributes that should be added to the rendered HTML tag.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    codeBlock: {\n      /**\n       * Set a code block\n       * @param attributes Code block attributes\n       * @example editor.commands.setCodeBlock({ language: 'javascript' })\n       */\n      setCodeBlock: (attributes?: { language: string }) => ReturnType\n      /**\n       * Toggle a code block\n       * @param attributes Code block attributes\n       * @example editor.commands.toggleCodeBlock({ language: 'javascript' })\n       */\n      toggleCodeBlock: (attributes?: { language: string }) => ReturnType\n    }\n  }\n}\n\n/**\n * Matches a code block with backticks.\n */\nexport const backtickInputRegex = /^```([a-z]+)?[\\s\\n]$/\n\n/**\n * Matches a code block with tildes.\n */\nexport const tildeInputRegex = /^~~~([a-z]+)?[\\s\\n]$/\n\n/**\n * This extension allows you to create code blocks.\n * @see https://tiptap.dev/api/nodes/code-block\n */\nexport const CodeBlock = Node.create<CodeBlockOptions>({\n  name: 'codeBlock',\n\n  addOptions() {\n    return {\n      languageClassPrefix: 'language-',\n      exitOnTripleEnter: true,\n      exitOnArrowDown: true,\n      defaultLanguage: null,\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'text*',\n\n  marks: '',\n\n  group: 'block',\n\n  code: true,\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      language: {\n        default: this.options.defaultLanguage,\n        parseHTML: element => {\n          const { languageClassPrefix } = this.options\n          const classNames = [...(element.firstElementChild?.classList || [])]\n          const languages = classNames\n            .filter(className => className.startsWith(languageClassPrefix))\n            .map(className => className.replace(languageClassPrefix, ''))\n          const language = languages[0]\n\n          if (!language) {\n            return null\n          }\n\n          return language\n        },\n        rendered: false,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'pre',\n        preserveWhitespace: 'full',\n      },\n    ]\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    return [\n      'pre',\n      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),\n      [\n        'code',\n        {\n          class: node.attrs.language ? this.options.languageClassPrefix + node.attrs.language : null,\n        },\n        0,\n      ],\n    ]\n  },\n\n  addCommands() {\n    return {\n      setCodeBlock:\n        attributes =>\n        ({ commands }) => {\n          return commands.setNode(this.name, attributes)\n        },\n      toggleCodeBlock:\n        attributes =>\n        ({ commands }) => {\n          return commands.toggleNode(this.name, 'paragraph', attributes)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Alt-c': () => this.editor.commands.toggleCodeBlock(),\n\n      // remove code block when at start of document or code block is empty\n      Backspace: () => {\n        const { empty, $anchor } = this.editor.state.selection\n        const isAtStart = $anchor.pos === 1\n\n        if (!empty || $anchor.parent.type.name !== this.name) {\n          return false\n        }\n\n        if (isAtStart || !$anchor.parent.textContent.length) {\n          return this.editor.commands.clearNodes()\n        }\n\n        return false\n      },\n\n      // exit node on triple enter\n      Enter: ({ editor }) => {\n        if (!this.options.exitOnTripleEnter) {\n          return false\n        }\n\n        const { state } = editor\n        const { selection } = state\n        const { $from, empty } = selection\n\n        if (!empty || $from.parent.type !== this.type) {\n          return false\n        }\n\n        const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2\n        const endsWithDoubleNewline = $from.parent.textContent.endsWith('\\n\\n')\n\n        if (!isAtEnd || !endsWithDoubleNewline) {\n          return false\n        }\n\n        return editor\n          .chain()\n          .command(({ tr }) => {\n            tr.delete($from.pos - 2, $from.pos)\n\n            return true\n          })\n          .exitCode()\n          .run()\n      },\n\n      // exit node on arrow down\n      ArrowDown: ({ editor }) => {\n        if (!this.options.exitOnArrowDown) {\n          return false\n        }\n\n        const { state } = editor\n        const { selection, doc } = state\n        const { $from, empty } = selection\n\n        if (!empty || $from.parent.type !== this.type) {\n          return false\n        }\n\n        const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2\n\n        if (!isAtEnd) {\n          return false\n        }\n\n        const after = $from.after()\n\n        if (after === undefined) {\n          return false\n        }\n\n        const nodeAfter = doc.nodeAt(after)\n\n        if (nodeAfter) {\n          return editor.commands.command(({ tr }) => {\n            tr.setSelection(Selection.near(doc.resolve(after)))\n            return true\n          })\n        }\n\n        return editor.commands.exitCode()\n      },\n    }\n  },\n\n  addInputRules() {\n    return [\n      textblockTypeInputRule({\n        find: backtickInputRegex,\n        type: this.type,\n        getAttributes: match => ({\n          language: match[1],\n        }),\n      }),\n      textblockTypeInputRule({\n        find: tildeInputRegex,\n        type: this.type,\n        getAttributes: match => ({\n          language: match[1],\n        }),\n      }),\n    ]\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      // this plugin creates a code block for pasted content from VS Code\n      // we can also detect the copied code language\n      new Plugin({\n        key: new PluginKey('codeBlockVSCodeHandler'),\n        props: {\n          handlePaste: (view, event) => {\n            if (!event.clipboardData) {\n              return false\n            }\n\n            // don’t create a new code block within code blocks\n            if (this.editor.isActive(this.type.name)) {\n              return false\n            }\n\n            const text = event.clipboardData.getData('text/plain')\n            const vscode = event.clipboardData.getData('vscode-editor-data')\n            const vscodeData = vscode ? JSON.parse(vscode) : undefined\n            const language = vscodeData?.mode\n\n            if (!text || !language) {\n              return false\n            }\n\n            const { tr, schema } = view.state\n\n            // prepare a text node\n            // strip carriage return chars from text pasted as code\n            // see: https://github.com/ProseMirror/prosemirror-view/commit/a50a6bcceb4ce52ac8fcc6162488d8875613aacd\n            const textNode = schema.text(text.replace(/\\r\\n?/g, '\\n'))\n\n            // create a code block with the text node\n            // replace selection with the code block\n            tr.replaceSelectionWith(this.type.create({ language }, textNode))\n\n            if (tr.selection.$from.parent.type !== this.type) {\n              // put cursor inside the newly created code block\n              tr.setSelection(TextSelection.near(tr.doc.resolve(Math.max(0, tr.selection.from - 2))))\n            }\n\n            // store meta information\n            // this is useful for other plugins that depends on the paste event\n            // like the paste rule plugin\n            tr.setMeta('paste', true)\n\n            view.dispatch(tr)\n\n            return true\n          },\n        },\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,kBAA8D;AAC9D,mBAA4D;AAsDrD,IAAM,qBAAqB;AAK3B,IAAM,kBAAkB;AAMxB,IAAM,YAAY,iBAAK,OAAyB;AAAA,EACrD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,EAET,OAAO;AAAA,EAEP,OAAO;AAAA,EAEP,MAAM;AAAA,EAEN,UAAU;AAAA,EAEV,gBAAgB;AACd,WAAO;AAAA,MACL,UAAU;AAAA,QACR,SAAS,KAAK,QAAQ;AAAA,QACtB,WAAW,aAAW;AA7F9B;AA8FU,gBAAM,EAAE,oBAAoB,IAAI,KAAK;AACrC,gBAAM,aAAa,CAAC,KAAI,aAAQ,sBAAR,mBAA2B,cAAa,CAAC,CAAE;AACnE,gBAAM,YAAY,WACf,OAAO,eAAa,UAAU,WAAW,mBAAmB,CAAC,EAC7D,IAAI,eAAa,UAAU,QAAQ,qBAAqB,EAAE,CAAC;AAC9D,gBAAM,WAAW,UAAU,CAAC;AAE5B,cAAI,CAAC,UAAU;AACb,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EAEA,YAAY;AACV,WAAO;AAAA,MACL;AAAA,QACE,KAAK;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAE,MAAM,eAAe,GAAG;AACnC,WAAO;AAAA,MACL;AAAA,UACA,6BAAgB,KAAK,QAAQ,gBAAgB,cAAc;AAAA,MAC3D;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO,KAAK,MAAM,WAAW,KAAK,QAAQ,sBAAsB,KAAK,MAAM,WAAW;AAAA,QACxF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,cACE,gBACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,QAAQ,KAAK,MAAM,UAAU;AAAA,MAC/C;AAAA,MACF,iBACE,gBACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,MAAM,aAAa,UAAU;AAAA,MAC/D;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,aAAa,MAAM,KAAK,OAAO,SAAS,gBAAgB;AAAA;AAAA,MAGxD,WAAW,MAAM;AACf,cAAM,EAAE,OAAO,QAAQ,IAAI,KAAK,OAAO,MAAM;AAC7C,cAAM,YAAY,QAAQ,QAAQ;AAElC,YAAI,CAAC,SAAS,QAAQ,OAAO,KAAK,SAAS,KAAK,MAAM;AACpD,iBAAO;AAAA,QACT;AAEA,YAAI,aAAa,CAAC,QAAQ,OAAO,YAAY,QAAQ;AACnD,iBAAO,KAAK,OAAO,SAAS,WAAW;AAAA,QACzC;AAEA,eAAO;AAAA,MACT;AAAA;AAAA,MAGA,OAAO,CAAC,EAAE,OAAO,MAAM;AACrB,YAAI,CAAC,KAAK,QAAQ,mBAAmB;AACnC,iBAAO;AAAA,QACT;AAEA,cAAM,EAAE,MAAM,IAAI;AAClB,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,OAAO,MAAM,IAAI;AAEzB,YAAI,CAAC,SAAS,MAAM,OAAO,SAAS,KAAK,MAAM;AAC7C,iBAAO;AAAA,QACT;AAEA,cAAM,UAAU,MAAM,iBAAiB,MAAM,OAAO,WAAW;AAC/D,cAAM,wBAAwB,MAAM,OAAO,YAAY,SAAS,MAAM;AAEtE,YAAI,CAAC,WAAW,CAAC,uBAAuB;AACtC,iBAAO;AAAA,QACT;AAEA,eAAO,OACJ,MAAM,EACN,QAAQ,CAAC,EAAE,GAAG,MAAM;AACnB,aAAG,OAAO,MAAM,MAAM,GAAG,MAAM,GAAG;AAElC,iBAAO;AAAA,QACT,CAAC,EACA,SAAS,EACT,IAAI;AAAA,MACT;AAAA;AAAA,MAGA,WAAW,CAAC,EAAE,OAAO,MAAM;AACzB,YAAI,CAAC,KAAK,QAAQ,iBAAiB;AACjC,iBAAO;AAAA,QACT;AAEA,cAAM,EAAE,MAAM,IAAI;AAClB,cAAM,EAAE,WAAW,IAAI,IAAI;AAC3B,cAAM,EAAE,OAAO,MAAM,IAAI;AAEzB,YAAI,CAAC,SAAS,MAAM,OAAO,SAAS,KAAK,MAAM;AAC7C,iBAAO;AAAA,QACT;AAEA,cAAM,UAAU,MAAM,iBAAiB,MAAM,OAAO,WAAW;AAE/D,YAAI,CAAC,SAAS;AACZ,iBAAO;AAAA,QACT;AAEA,cAAM,QAAQ,MAAM,MAAM;AAE1B,YAAI,UAAU,QAAW;AACvB,iBAAO;AAAA,QACT;AAEA,cAAM,YAAY,IAAI,OAAO,KAAK;AAElC,YAAI,WAAW;AACb,iBAAO,OAAO,SAAS,QAAQ,CAAC,EAAE,GAAG,MAAM;AACzC,eAAG,aAAa,uBAAU,KAAK,IAAI,QAAQ,KAAK,CAAC,CAAC;AAClD,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AAEA,eAAO,OAAO,SAAS,SAAS;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,WAAO;AAAA,UACL,oCAAuB;AAAA,QACrB,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,QACX,eAAe,YAAU;AAAA,UACvB,UAAU,MAAM,CAAC;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,UACD,oCAAuB;AAAA,QACrB,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,QACX,eAAe,YAAU;AAAA,UACvB,UAAU,MAAM,CAAC;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,wBAAwB;AACtB,WAAO;AAAA;AAAA;AAAA,MAGL,IAAI,oBAAO;AAAA,QACT,KAAK,IAAI,uBAAU,wBAAwB;AAAA,QAC3C,OAAO;AAAA,UACL,aAAa,CAAC,MAAM,UAAU;AAC5B,gBAAI,CAAC,MAAM,eAAe;AACxB,qBAAO;AAAA,YACT;AAGA,gBAAI,KAAK,OAAO,SAAS,KAAK,KAAK,IAAI,GAAG;AACxC,qBAAO;AAAA,YACT;AAEA,kBAAM,OAAO,MAAM,cAAc,QAAQ,YAAY;AACrD,kBAAM,SAAS,MAAM,cAAc,QAAQ,oBAAoB;AAC/D,kBAAM,aAAa,SAAS,KAAK,MAAM,MAAM,IAAI;AACjD,kBAAM,WAAW,yCAAY;AAE7B,gBAAI,CAAC,QAAQ,CAAC,UAAU;AACtB,qBAAO;AAAA,YACT;AAEA,kBAAM,EAAE,IAAI,OAAO,IAAI,KAAK;AAK5B,kBAAM,WAAW,OAAO,KAAK,KAAK,QAAQ,UAAU,IAAI,CAAC;AAIzD,eAAG,qBAAqB,KAAK,KAAK,OAAO,EAAE,SAAS,GAAG,QAAQ,CAAC;AAEhE,gBAAI,GAAG,UAAU,MAAM,OAAO,SAAS,KAAK,MAAM;AAEhD,iBAAG,aAAa,2BAAc,KAAK,GAAG,IAAI,QAAQ,KAAK,IAAI,GAAG,GAAG,UAAU,OAAO,CAAC,CAAC,CAAC,CAAC;AAAA,YACxF;AAKA,eAAG,QAAQ,SAAS,IAAI;AAExB,iBAAK,SAAS,EAAE;AAEhB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ADxTD,IAAO,gBAAQ;", "names": []}