{"name": "prosemirror-dropcursor", "version": "1.8.2", "description": "Drop cursor plugin for ProseMirror", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "sideEffects": false, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}], "repository": {"type": "git", "url": "git://github.com/prosemirror/prosemirror-dropcursor.git"}, "dependencies": {"prosemirror-state": "^1.0.0", "prosemirror-view": "^1.1.0", "prosemirror-transform": "^1.1.0"}, "devDependencies": {"@prosemirror/buildhelper": "^0.1.5"}, "scripts": {"test": "pm-runtests", "prepare": "pm-buildhelper src/dropcursor.ts"}}