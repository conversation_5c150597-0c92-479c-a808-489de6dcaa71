{"name": "prosemirror-tables", "version": "1.7.1", "description": "ProseMirror's rowspan/colspan tables component", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "style": "style/tables.css", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./style/tables.css": "./style/tables.css"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/prosemirror/prosemirror-tables.git"}, "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "files": ["style", "dist"], "dependencies": {"prosemirror-keymap": "^1.2.2", "prosemirror-model": "^1.25.0", "prosemirror-state": "^1.4.3", "prosemirror-transform": "^1.10.3", "prosemirror-view": "^1.39.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "@vitest/coverage-v8": "3.1.1", "builddocs": "^1.0.7", "eslint": "^8.57.0", "eslint-plugin-jest": "^26.9.0", "happy-dom": "^16.8.1", "ist": "^1.1.7", "prettier": "^3.5.3", "prosemirror-commands": "^1.7.1", "prosemirror-example-setup": "^1.2.3", "prosemirror-gapcursor": "^1.3.2", "prosemirror-menu": "^1.2.4", "prosemirror-schema-basic": "^1.2.4", "prosemirror-test-builder": "^1.1.1", "tsup": "^8.4.0", "typescript": "^5.7.3", "vite": "^6.2.6", "vitest": "^3.1.1"}, "scripts": {"dev": "vite demo", "build_demo": "vite build demo", "typecheck": "tsc --noEmit", "test": "vitest", "build": "tsup", "watch": "tsup --watch", "build_readme": "builddocs --name tables --format markdown --main src/README.md src/*.js > README.md", "format": "prettier --write .", "lint": "eslint ./src/ ./test/ && prettier --check .", "fix": "eslint --fix ./src/ ./test/ && prettier --write ."}}