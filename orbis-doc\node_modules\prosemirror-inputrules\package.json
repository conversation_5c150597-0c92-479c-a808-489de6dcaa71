{"name": "prosemirror-<PERSON><PERSON><PERSON>", "version": "1.5.0", "description": "Automatic transforms on text input for ProseMirror", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "sideEffects": false, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}], "repository": {"type": "git", "url": "git://github.com/prosemirror/prosemirror-inputrules.git"}, "dependencies": {"prosemirror-state": "^1.0.0", "prosemirror-transform": "^1.0.0"}, "devDependencies": {"@prosemirror/buildhelper": "^0.1.5"}, "scripts": {"prepare": "pm-buildhelper src/index.ts"}}