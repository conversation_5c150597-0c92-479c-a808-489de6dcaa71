{"version": 3, "sources": ["../src/text.ts", "../src/index.ts"], "sourcesContent": ["import { Node } from '@tiptap/core'\n\n/**\n * This extension allows you to create text nodes.\n * @see https://www.tiptap.dev/api/nodes/text\n */\nexport const Text = Node.create({\n  name: 'text',\n  group: 'inline',\n})\n", "import { Text } from './text.js'\n\nexport * from './text.js'\n\nexport default Text\n"], "mappings": ";AAAA,SAAS,YAAY;AAMd,IAAM,OAAO,KAAK,OAAO;AAAA,EAC9B,MAAM;AAAA,EACN,OAAO;AACT,CAAC;;;ACLD,IAAO,gBAAQ;", "names": []}