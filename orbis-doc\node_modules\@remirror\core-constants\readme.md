# @remirror/core-constants

> core constants used throughout the `remirror` codebase.

[![Version][version]][npm] [![Weekly Downloads][downloads-badge]][npm] [![Bundled size][size-badge]][size] [![Typed Codebase][typescript]](#) [![MIT License][license]](#)

[version]: https://flat.badgen.net/npm/v/@remirror/core-constants
[npm]: https://npmjs.com/package/@remirror/core-constants
[license]: https://flat.badgen.net/badge/license/MIT/purple
[size]: https://bundlephobia.com/result?p=@remirror/core-constants
[size-badge]: https://flat.badgen.net/bundlephobia/minzip/@remirror/core-constants
[typescript]: https://flat.badgen.net/badge/icon/TypeScript?icon=typescript&label
[downloads-badge]: https://badgen.net/npm/dw/@remirror/core-constants/red?icon=npm

## Installation

This is included by default when you install the recommended `remirror` package. All exports are also available via `remirror/core/constants` and `remirror/core`.
