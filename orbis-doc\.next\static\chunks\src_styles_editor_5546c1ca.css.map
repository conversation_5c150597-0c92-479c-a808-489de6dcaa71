{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/DEV/orbis-doc/src/styles/editor.css"], "sourcesContent": ["/* Styles pour l'éditeur Tiptap avec pagination */\n\n.tiptap-editor {\n  outline: none;\n  border: none;\n  background: #f7f7f7;\n  min-height: 100vh;\n}\n\n/* Styles pour les pages avec PaginationPlus */\n.tiptap-editor .page {\n  width: 595px; /* A4 width in pixels (210mm) */\n  min-height: 842px; /* A4 height in pixels (297mm) */\n  margin: 0 auto 20px auto;\n  background: white;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  padding: 60px 80px; /* Marges intérieures */\n  box-sizing: border-box;\n  position: relative;\n}\n\n/* Styles pour l'en-tête */\n.tiptap-pagination-header {\n  position: absolute;\n  top: 10mm;\n  left: 25mm;\n  right: 25mm;\n  height: 15mm;\n  border-bottom: 1px solid #e5e7eb;\n  display: flex;\n  align-items: center;\n  font-size: 12px;\n  color: #6b7280;\n}\n\n/* Styles pour le pied de page */\n.tiptap-pagination-footer {\n  position: absolute;\n  bottom: 10mm;\n  left: 25mm;\n  right: 25mm;\n  height: 15mm;\n  border-top: 1px solid #e5e7eb;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: #6b7280;\n}\n\n/* Styles pour le contenu de la page */\n.tiptap-pagination-content {\n  min-height: calc(297mm - 50mm); /* Hauteur totale moins marges top/bottom */\n  padding-top: 20mm; /* Espace pour l'en-tête */\n  padding-bottom: 20mm; /* Espace pour le pied de page */\n}\n\n/* Styles généraux pour le contenu */\n.tiptap-editor p {\n  margin: 0 0 1em 0;\n  line-height: 1.6;\n}\n\n.tiptap-editor h1,\n.tiptap-editor h2,\n.tiptap-editor h3,\n.tiptap-editor h4,\n.tiptap-editor h5,\n.tiptap-editor h6 {\n  margin: 1.5em 0 0.5em 0;\n  font-weight: bold;\n}\n\n.tiptap-editor h1 { font-size: 2em; }\n.tiptap-editor h2 { font-size: 1.5em; }\n.tiptap-editor h3 { font-size: 1.25em; }\n\n.tiptap-editor ul,\n.tiptap-editor ol {\n  margin: 1em 0;\n  padding-left: 2em;\n}\n\n.tiptap-editor blockquote {\n  border-left: 4px solid #e5e7eb;\n  padding-left: 1em;\n  margin: 1em 0;\n  font-style: italic;\n}\n\n.tiptap-editor code {\n  background: #f3f4f6;\n  padding: 0.2em 0.4em;\n  border-radius: 3px;\n  font-family: monospace;\n}\n\n.tiptap-editor pre {\n  background: #f3f4f6;\n  padding: 1em;\n  border-radius: 6px;\n  overflow-x: auto;\n  margin: 1em 0;\n}\n\n.tiptap-editor pre code {\n  background: none;\n  padding: 0;\n}\n\n/* Styles spécifiques pour PaginationPlus */\n.tiptap-editor .pagination-plus-page {\n  width: 595px !important;\n  min-height: 842px !important;\n  margin: 0 auto 20px auto !important;\n  background: white !important;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;\n  padding: 60px 80px !important;\n  box-sizing: border-box !important;\n  position: relative !important;\n}\n\n.tiptap-editor .pagination-plus-header {\n  position: absolute !important;\n  top: 20px !important;\n  left: 80px !important;\n  right: 80px !important;\n  height: 30px !important;\n  border-bottom: 1px solid #e5e7eb !important;\n  display: flex !important;\n  align-items: center !important;\n  font-size: 12px !important;\n  color: #6b7280 !important;\n}\n\n.tiptap-editor .pagination-plus-footer {\n  position: absolute !important;\n  bottom: 20px !important;\n  left: 80px !important;\n  right: 80px !important;\n  height: 30px !important;\n  border-top: 1px solid #e5e7eb !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  font-size: 12px !important;\n  color: #6b7280 !important;\n}\n\n/* Responsive pour l'aperçu */\n@media screen and (max-width: 768px) {\n  .tiptap-editor .page,\n  .tiptap-editor .pagination-plus-page {\n    width: 100% !important;\n    min-height: auto !important;\n    margin: 0 0 20px 0 !important;\n    padding: 20px !important;\n    box-shadow: none !important;\n    border: 1px solid #e5e7eb !important;\n  }\n\n  .tiptap-pagination-header,\n  .tiptap-pagination-footer,\n  .tiptap-editor .pagination-plus-header,\n  .tiptap-editor .pagination-plus-footer {\n    position: static !important;\n    margin: 10px 0 !important;\n  }\n\n  .tiptap-pagination-content {\n    min-height: auto !important;\n    padding: 0 !important;\n  }\n}\n"], "names": [], "mappings": "AAEA;;;;;;;AAQA;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;AAeA;;;;;;AAOA;;;;;AAKA;;;;;AAUA;;;;AACA;;;;AACA;;;;AAEA;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;AAQA;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;;;;;;;;AAaA;;;;;;;;;;;;;;AAeA;EACE;;;;;;;;;EAUA;;;;;EAQA", "debugId": null}}]}