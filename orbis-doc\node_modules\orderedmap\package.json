{"name": "orderedmap", "version": "2.1.1", "description": "Persistent ordered mapping from strings", "type": "module", "main": "dist/index.cjs", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "sideEffects": false, "files": ["dist/*"], "repository": {"type": "git", "url": "git+https://github.com/marijnh/orderedmap.git"}, "keywords": ["persistent", "map"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/marijnh/orderedmap/issues"}, "homepage": "https://github.com/marijnh/orderedmap#readme", "scripts": {"build": "rollup -c", "watch": "rollup -c -w", "prepare": "npm run build"}, "devDependencies": {"rollup": "^1.26.3"}}