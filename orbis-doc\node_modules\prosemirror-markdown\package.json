{"name": "prosemirror-markdown", "version": "1.13.2", "description": "ProseMirror Markdown integration", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "sideEffects": false, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}], "repository": {"type": "git", "url": "git://github.com/prosemirror/prosemirror-markdown.git"}, "dependencies": {"markdown-it": "^14.0.0", "prosemirror-model": "^1.25.0", "@types/markdown-it": "^14.0.0"}, "devDependencies": {"@prosemirror/buildhelper": "^0.1.5", "prosemirror-test-builder": "^1.0.0", "punycode": "^1.4.0"}, "scripts": {"test": "pm-runtests", "prepare": "pm-buildhelper src/index.ts"}}