## 1.2.5 (2025-04-22)

### Bug fixes

Make sure the menu is re-rendered when the editor's root changes, so that it doesn't reference icons whose SVG lives in another root.

## 1.2.4 (2023-08-20)

### Bug fixes

Fix a bug where icon creation crashed because it couldn't find a Document value.

## 1.2.3 (2023-08-16)

### Bug fixes

Don't directly use the global `window`/`document`, to fix use in a different frame or shadow root.

## 1.2.2 (2023-05-17)

### Bug fixes

Include CommonJS type declarations in the package to please new TypeScript resolution settings.

## 1.2.1 (2022-06-22)

### Bug fixes

Export CSS file from package.json.

## 1.2.0 (2022-05-30)

### New features

Include TypeScript type declarations.

## 1.1.4 (2020-03-12)

### Bug fixes

Restore compatibility with IE11.

## 1.1.3 (2020-03-04)

### Bug fixes

Update crel dependency to a version that exposes an ES module.

## 1.1.2 (2019-12-02)

### Bug fixes

Downgrade a dependency so that the package can run in IE11 again.

## 1.1.1 (2019-11-20)

### Bug fixes

The file referred to in the package's `module` field now is compiled down to ES5.

Rename ES module files to use a .js extension, since Webpack gets confused by .mjs

## 1.1.0 (2019-11-08)

### New features

Add a `module` field to package json file.

## 1.0.5 (2018-07-19)

### Bug fixes

Fix issue where menu items would still execute their command when clicked even if disabled.

## 1.0.4 (2018-03-09)

### Bug fixes

Fixes a bug that prevented the menu bar from properly unregistering its `"scroll"` event handlers when destroyed.

## 1.0.3 (2018-02-15)

### Bug fixes

The floating menu bar now works better in a scrollable parent node.

## 1.0.2 (2018-01-17)

### Bug fixes

Make the `render` property of a menu item spec work as documented again.

## 1.0.1 (2017-10-18)

### Bug fixes
    
The menu no longer flips enabled/disabled styles on each update in IE11.

## 1.0.0 (2017-10-13)

First stable release.
