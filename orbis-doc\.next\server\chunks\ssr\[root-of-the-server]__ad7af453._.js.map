{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/DEV/orbis-doc/src/components/DocumentEditor.tsx"], "sourcesContent": ["'use client'\n\nimport { useEditor, EditorContent } from '@tiptap/react'\nimport StarterKit from '@tiptap/starter-kit'\nimport { useEffect, useState } from 'react'\nimport {\n  PaginationPlus,\n  TablePlus,\n  TableRowPlus,\n  TableCellPlus,\n  TableHeaderPlus\n} from 'tiptap-pagination-plus'\nimport '../styles/editor.css'\n\ninterface DocumentEditorProps {\n  content?: string\n  onChange?: (content: string) => void\n}\n\nexport default function DocumentEditor({ content = '', onChange }: DocumentEditorProps) {\n  const [isMounted, setIsMounted] = useState(false)\n\n  // Gérer l'hydratation côté client\n  useEffect(() => {\n    setIsMounted(true)\n  }, [])\n\n  const editor = useEditor(\n    isMounted ? {\n      extensions: [\n        StarterKit,\n        TablePlus,\n        TableRowPlus,\n        TableCellPlus,\n        TableHeaderPlus,\n        PaginationPlus.configure({\n          pageHeight: 842,        // Height of each page in pixels (A4 height)\n          pageGap: 20,           // Gap between pages in pixels\n          pageBreakBackground: \"#f7f7f7\",  // Background color for page gaps\n          pageHeaderHeight: 50,   // Height of page header/footer in pixels\n          footerText: \"Document Orbis\"  // Custom footer text\n        }),\n      ],\n      content,\n      immediatelyRender: false, // Éviter les erreurs d'hydratation SSR\n      onUpdate: ({ editor }) => {\n        const html = editor.getHTML()\n        onChange?.(html)\n      },\n      editorProps: {\n        attributes: {\n          class: 'tiptap-editor focus:outline-none min-h-screen p-4',\n        },\n      },\n    } : null\n  )\n\n  // Éviter les erreurs d'hydratation\n  if (!isMounted) {\n    return (\n      <div className=\"min-h-screen bg-gray-100 p-8\">\n        <div className=\"max-w-4xl mx-auto bg-white shadow-lg\">\n          <div className=\"h-96 flex items-center justify-center text-gray-500\">\n            Chargement de l'éditeur...\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-100\">\n      <EditorContent editor={editor} />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;;AAmBe,SAAS,eAAe,EAAE,UAAU,EAAE,EAAE,QAAQ,EAAuB;IACpF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EACrB,YAAY;QACV,YAAY;YACV,2JAAA,CAAA,UAAU;YACV,iKAAA,CAAA,YAAS;YACT,oKAAA,CAAA,eAAY;YACZ,qKAAA,CAAA,gBAAa;YACb,uKAAA,CAAA,kBAAe;YACf,sKAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;gBACvB,YAAY;gBACZ,SAAS;gBACT,qBAAqB;gBACrB,kBAAkB;gBAClB,YAAY,iBAAkB,qBAAqB;YACrD;SACD;QACD;QACA,mBAAmB;QACnB,UAAU,CAAC,EAAE,MAAM,EAAE;YACnB,MAAM,OAAO,OAAO,OAAO;YAC3B,WAAW;QACb;QACA,aAAa;YACX,YAAY;gBACV,OAAO;YACT;QACF;IACF,IAAI;IAGN,mCAAmC;IACnC,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAAsD;;;;;;;;;;;;;;;;IAM7E;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,kKAAA,CAAA,gBAAa;YAAC,QAAQ;;;;;;;;;;;AAG7B", "debugId": null}}]}